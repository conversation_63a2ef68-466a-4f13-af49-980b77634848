#!/usr/bin/env python3
"""
Quick test of sample video - process first 60 seconds only for faster results
"""

import os
import sys
import asyncio
import tempfile
import shutil
from dotenv import load_dotenv

# Load environment
load_dotenv('.env')

# Add paths
sys.path.append('backend')
sys.path.append('worker')

def test_sample_video_quick():
    """Test first 60 seconds of the sample video for quick results"""
    print("🎮 Quick Test - Sample Gameplay Video (First 60 seconds)")
    print("=" * 60)
    
    # Video file path
    video_path = "backend/samples/24 Kills + MANIAC!! Alucard Best Build Exp Lane!! - Build Top 1 Global Alucard ~ MLBB.mp4"
    
    if not os.path.exists(video_path):
        print("❌ Sample video not found!")
        return False
    
    # Get video info
    file_size = os.path.getsize(video_path)
    print(f"📹 Video: {os.path.basename(video_path)}")
    print(f"📊 Size: {file_size / (1024*1024):.1f} MB")
    print("⏱️  Processing: First 60 seconds only (for quick test)")
    
    try:
        # Test video processor initialization
        from processors.video_processor import VideoProcessor
        processor = VideoProcessor()
        print("✅ Video processor initialized")
        
        # Show configured events
        print(f"🎯 Configured to detect {len(processor.highlight_phrases)} gaming events:")
        events_list = list(processor.highlight_phrases.keys())
        for i in range(0, len(events_list), 4):
            row = events_list[i:i+4]
            print(f"   {', '.join(row)}")
        
        # Create temporary directory for processing
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"\n📁 Temp directory: {temp_dir}")
            
            # Create a 60-second clip for testing
            temp_video_path = os.path.join(temp_dir, "sample_60s.mp4")
            
            print("✂️  Creating 60-second test clip...")
            import ffmpeg
            
            # Extract first 60 seconds
            (
                ffmpeg
                .input(video_path, t=60)  # First 60 seconds
                .output(temp_video_path, vcodec='copy', acodec='copy')
                .overwrite_output()
                .run(quiet=True)
            )
            
            clip_size = os.path.getsize(temp_video_path)
            print(f"✅ Test clip created: {clip_size / (1024*1024):.1f} MB")
            
            async def process_video():
                """Process the 60-second video clip"""
                print("\n🔊 Step 1: Extracting transcript with Whisper AI...")
                
                # Initialize Whisper model (use tiny for speed)
                import whisper
                model = whisper.load_model("tiny")
                processor.whisper_model = model
                print("✅ Whisper model loaded (tiny for speed)")
                
                # Extract transcript
                transcript = await processor.extract_transcript(temp_video_path)
                segments = transcript.get('segments', [])
                print(f"✅ Transcript extracted: {len(segments)} segments")
                
                # Show transcript content
                if segments:
                    print("\n📝 Transcript content:")
                    for i, segment in enumerate(segments):
                        text = segment.get('text', '').strip()
                        start = segment.get('start', 0)
                        end = segment.get('end', 0)
                        print(f"   [{start:5.1f}s-{end:5.1f}s]: {text}")
                        
                        # Look for gaming terms in transcript
                        text_lower = text.lower()
                        gaming_terms = ['kill', 'blood', 'enemy', 'slain', 'double', 'triple', 'mega', 'maniac', 'savage', 'godlike', 'legendary', 'spree']
                        found_terms = [term for term in gaming_terms if term in text_lower]
                        if found_terms:
                            print(f"                Gaming terms found: {', '.join(found_terms)}")
                
                print("\n🎯 Step 2: Detecting gaming highlights...")
                
                # Detect highlights
                highlight_ranges = await processor.detect_highlights(transcript)
                print(f"✅ Detected {len(highlight_ranges)} highlight ranges")
                
                if highlight_ranges:
                    print("\n🎬 Detected Highlights:")
                    for i, (start, end) in enumerate(highlight_ranges):
                        duration = end - start
                        print(f"   Clip {i+1}: {start:5.1f}s - {end:5.1f}s ({duration:4.1f}s duration)")
                    
                    # Analyze the types of highlights
                    total_duration = sum(end - start for start, end in highlight_ranges)
                    avg_duration = total_duration / len(highlight_ranges)
                    
                    print(f"\n📊 Analysis:")
                    print(f"   • Total highlight duration: {total_duration:.1f} seconds")
                    print(f"   • Average clip length: {avg_duration:.1f} seconds")
                    
                    # Categorize clips by length (longer clips likely multi-kill sequences)
                    short_clips = [r for r in highlight_ranges if (r[1] - r[0]) <= 5]
                    medium_clips = [r for r in highlight_ranges if 5 < (r[1] - r[0]) <= 10]
                    long_clips = [r for r in highlight_ranges if (r[1] - r[0]) > 10]
                    
                    print(f"   • Short clips (≤5s): {len(short_clips)} - likely single events")
                    print(f"   • Medium clips (5-10s): {len(medium_clips)} - likely double/triple kills")
                    print(f"   • Long clips (>10s): {len(long_clips)} - likely multi-kill sequences")
                    
                    return {
                        'transcript_segments': len(segments),
                        'highlight_ranges': highlight_ranges,
                        'total_duration': total_duration,
                        'success': True
                    }
                else:
                    print("⚠️  No gaming highlights detected in first 60 seconds")
                    print("💡 This could mean:")
                    print("   • Gaming events occur later in the video")
                    print("   • Audio quality needs improvement")
                    print("   • Different language or accent")
                    return {
                        'transcript_segments': len(segments),
                        'highlight_ranges': [],
                        'total_duration': 0,
                        'success': True
                    }
            
            # Run the async processing
            result = asyncio.run(process_video())
            
            if result['success']:
                print("\n" + "=" * 60)
                print("🎉 QUICK TEST COMPLETE!")
                print(f"📊 Results Summary:")
                print(f"   • Transcript segments: {result['transcript_segments']}")
                print(f"   • Highlight ranges found: {len(result['highlight_ranges'])}")
                if result['highlight_ranges']:
                    print(f"   • Total highlight duration: {result['total_duration']:.1f}s")
                
                print(f"\n💡 What this means:")
                if result['highlight_ranges']:
                    print(f"   ✅ Gaming events detected in audio!")
                    print(f"   ✅ Highlight detection is working")
                    print(f"   ✅ Multi-kill sequences would be captured")
                else:
                    print(f"   ⚠️  No events in first 60 seconds")
                    print(f"   💡 Try processing the full video")
                    print(f"   💡 Gaming action might be later in video")
                
                return True
            else:
                print("❌ Quick test failed")
                return False
                
    except Exception as e:
        print(f"❌ Error in quick test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the quick test"""
    print("🚀 Video Highlight Extractor - Quick Sample Test")
    print("Testing first 60 seconds of MLBB Alucard gameplay")
    
    success = test_sample_video_quick()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 QUICK TEST SUCCESSFUL!")
        print("\n🚀 Ready for full system test:")
        print("   1. Start the system: make up-prod")
        print("   2. Visit http://localhost:3000")
        print("   3. Upload the full video")
        print("   4. Get complete highlight clips!")
    else:
        print("\n❌ Quick test failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
