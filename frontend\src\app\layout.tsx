import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Video Highlight Extractor',
  description: 'AI-powered gaming highlight extraction from uploaded videos',
  keywords: ['video', 'highlights', 'gaming', 'AI', 'clips'],
  authors: [{ name: 'Video Highlight Team' }],
  viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
          <header className="bg-white shadow-sm border-b border-gray-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center h-16">
                <div className="flex items-center">
                  <h1 className="text-xl font-bold text-gray-900">
                    🎮 Video Highlight Extractor
                  </h1>
                </div>
                <div className="text-sm text-gray-500">
                  AI-Powered Gaming Clips
                </div>
              </div>
            </div>
          </header>
          
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {children}
          </main>
          
          <footer className="bg-white border-t border-gray-200 mt-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <div className="text-center text-sm text-gray-500">
                <p>© 2024 Video Highlight Extractor. Built with Next.js, FastAPI, and AI.</p>
              </div>
            </div>
          </footer>
        </div>
      </body>
    </html>
  );
}
