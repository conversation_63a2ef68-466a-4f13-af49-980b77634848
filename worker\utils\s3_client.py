"""
S3 client utilities for the worker.
"""

import logging
import os
from typing import Optional

import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from botocore.config import Config

from core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class S3Manager:
    """S3 operations manager for the worker."""
    
    def __init__(self):
        config = Config(
            region_name=settings.AWS_REGION,
            retries={'max_attempts': 3, 'mode': 'adaptive'},
            max_pool_connections=50,
        )
        
        self.s3 = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            endpoint_url=settings.S3_ENDPOINT_URL if settings.S3_ENDPOINT_URL else None,
            config=config,
        )
        
        self.input_bucket = settings.INPUT_BUCKET
        self.output_bucket = settings.OUTPUT_BUCKET
    
    def ensure_buckets_exist(self) -> None:
        """Ensure required S3 buckets exist."""
        for bucket in [self.input_bucket, self.output_bucket]:
            try:
                self.s3.head_bucket(Bucket=bucket)
                logger.info(f"Bucket {bucket} exists")
            except ClientError as e:
                error_code = e.response['Error']['Code']
                if error_code == '404':
                    try:
                        self.s3.create_bucket(Bucket=bucket)
                        logger.info(f"Created bucket {bucket}")
                    except ClientError as create_error:
                        logger.error(f"Failed to create bucket {bucket}: {create_error}")
                        raise
                else:
                    logger.error(f"Error checking bucket {bucket}: {e}")
                    raise
    
    def download_file(self, bucket: str, object_key: str, local_path: str) -> None:
        """Download file from S3 to local path."""
        try:
            # Ensure local directory exists
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # Download file
            self.s3.download_file(bucket, object_key, local_path)
            logger.info(f"Downloaded {bucket}/{object_key} to {local_path}")
            
        except ClientError as e:
            logger.error(f"Failed to download {bucket}/{object_key}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error downloading {bucket}/{object_key}: {e}")
            raise
    
    def upload_file(self, local_path: str, bucket: str, object_key: str) -> None:
        """Upload file from local path to S3."""
        try:
            # Check if local file exists
            if not os.path.exists(local_path):
                raise FileNotFoundError(f"Local file not found: {local_path}")
            
            # Upload file
            self.s3.upload_file(local_path, bucket, object_key)
            logger.info(f"Uploaded {local_path} to {bucket}/{object_key}")
            
        except ClientError as e:
            logger.error(f"Failed to upload {local_path} to {bucket}/{object_key}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error uploading {local_path}: {e}")
            raise
    
    def object_exists(self, bucket: str, object_key: str) -> bool:
        """Check if object exists in S3."""
        try:
            self.s3.head_object(Bucket=bucket, Key=object_key)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            raise
    
    def get_object_size(self, bucket: str, object_key: str) -> Optional[int]:
        """Get object size in bytes."""
        try:
            response = self.s3.head_object(Bucket=bucket, Key=object_key)
            return response.get('ContentLength', 0)
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return None
            logger.error(f"Failed to get object size for {bucket}/{object_key}: {e}")
            raise
    
    def delete_object(self, bucket: str, object_key: str) -> bool:
        """Delete object from S3."""
        try:
            self.s3.delete_object(Bucket=bucket, Key=object_key)
            logger.info(f"Deleted object {bucket}/{object_key}")
            return True
        except ClientError as e:
            logger.error(f"Failed to delete object {bucket}/{object_key}: {e}")
            return False
    
    def list_objects(self, bucket: str, prefix: str = "") -> list:
        """List objects in S3 bucket with given prefix."""
        try:
            response = self.s3.list_objects_v2(
                Bucket=bucket,
                Prefix=prefix
            )
            
            objects = []
            for obj in response.get('Contents', []):
                objects.append({
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'],
                    'etag': obj['ETag'].strip('"'),
                })
            
            return objects
            
        except ClientError as e:
            logger.error(f"Failed to list objects in {bucket}/{prefix}: {e}")
            raise
