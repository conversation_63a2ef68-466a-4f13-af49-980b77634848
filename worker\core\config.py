"""
Worker configuration using Pydantic settings.
"""

import json
from typing import Dict, Any
from functools import lru_cache

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Worker settings loaded from environment variables."""
    
    # Environment
    ENVIRONMENT: str = Field(default="development", description="Environment")
    DEBUG: bool = Field(default=True, description="Debug mode")
    LOG_LEVEL: str = Field(default="INFO", description="Log level")
    
    # Redis Configuration
    REDIS_URL: str = Field(default="redis://localhost:6379/0", description="Redis URL")
    REDIS_HOST: str = Field(default="localhost", description="Redis host")
    REDIS_PORT: int = Field(default=6379, description="Redis port")
    REDIS_DB: int = Field(default=0, description="Redis database")
    
    # Backblaze B2 S3-Compatible Configuration
    BACKBLAZE_ACCESS_KEY_ID: str = Field(default="test", description="Backblaze B2 access key")
    BACKBLAZE_SECRET_ACCESS_KEY: str = Field(default="test", description="Backblaze B2 secret key")
    BACKBLAZE_REGION: str = Field(default="us-east-1", description="Backblaze B2 region")
    BACKBLAZE_ENDPOINT_URL: str = Field(default="http://localhost:4566", description="Backblaze B2 endpoint URL")
    INPUT_BUCKET: str = Field(default="video-uploads", description="Input S3 bucket")
    OUTPUT_BUCKET: str = Field(default="video-highlights", description="Output S3 bucket")

    # Legacy AWS compatibility properties (for boto3 client)
    @property
    def AWS_ACCESS_KEY_ID(self) -> str:
        return self.BACKBLAZE_ACCESS_KEY_ID

    @property
    def AWS_SECRET_ACCESS_KEY(self) -> str:
        return self.BACKBLAZE_SECRET_ACCESS_KEY

    @property
    def AWS_REGION(self) -> str:
        return self.BACKBLAZE_REGION

    @property
    def S3_ENDPOINT_URL(self) -> str:
        return self.BACKBLAZE_ENDPOINT_URL
    
    # Worker Configuration
    WORKER_QUEUE_NAME: str = Field(default="video_processing", description="Worker queue name")
    WORKER_CONCURRENCY: int = Field(default=2, description="Worker concurrency")
    JOB_TIMEOUT: int = Field(default=3600, description="Job timeout in seconds")
    
    # Video Processing Configuration
    WHISPER_MODEL: str = Field(default="medium", description="Whisper model size")
    TEMP_DIR: str = Field(default="/tmp/video_processing", description="Temporary directory")
    
    # Enhanced Highlight Detection Configuration
    HIGHLIGHT_PHRASES: str = Field(
        default='{"double_kill": [-5, 3, true], "first_blood": [-3, 2, false], "godlike": [-8, 5, true], "you_have_slain_an_enemy": [-2, 1, false], "killing_spree": [-5, 3, true], "legendary": [-8, 5, true], "maniac": [-12, 6, true], "mega_kill": [-8, 5, true], "monster_kill": [-4, 3, true], "triple_kill": [-8, 5, true], "unstoppable": [-8, 5, true], "savage": [-15, 7, true]}',
        description="Enhanced highlight phrases with timing and sequence capture settings"
    )
    MULTI_KILL_SEQUENCES: str = Field(
        default='{"double_kill": [["first_blood", "double_kill"], ["you_have_slain_an_enemy", "double_kill"]], "triple_kill": ["you_have_slain_an_enemy", "double_kill", "triple_kill"], "mega_kill": ["you_have_slain_an_enemy", "double_kill", "triple_kill", "mega_kill"], "maniac": ["you_have_slain_an_enemy", "double_kill", "triple_kill", "mega_kill", "maniac"], "savage": ["you_have_slain_an_enemy", "double_kill", "triple_kill", "mega_kill", "maniac", "savage"], "godlike": ["killing_spree", "legendary", "godlike"], "unstoppable": ["killing_spree", "legendary", "godlike", "unstoppable"]}',
        description="Multi-kill sequence definitions for enhanced highlight capture"
    )
    MERGE_GAP_THRESHOLD: float = Field(default=2.0, description="Gap threshold for merging clips")
    
    @validator('HIGHLIGHT_PHRASES')
    def parse_highlight_phrases(cls, v):
        """Parse highlight phrases from JSON string."""
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON format for HIGHLIGHT_PHRASES")
        return v

    @validator('MULTI_KILL_SEQUENCES')
    def parse_multi_kill_sequences(cls, v):
        """Parse multi-kill sequences from JSON string."""
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON format for MULTI_KILL_SEQUENCES")
        return v
    
    # FFmpeg Configuration
    FFMPEG_THREADS: int = Field(default=2, description="FFmpeg thread count")
    VIDEO_CODEC: str = Field(default="copy", description="Video codec for clips")
    AUDIO_CODEC: str = Field(default="copy", description="Audio codec for clips")
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields from environment


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
