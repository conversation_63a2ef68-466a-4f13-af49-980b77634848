"""
Worker configuration using Pydantic settings.
"""

import json
from typing import Dict, Any
from functools import lru_cache

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Worker settings loaded from environment variables."""
    
    # Environment
    ENVIRONMENT: str = Field(default="development", description="Environment")
    DEBUG: bool = Field(default=True, description="Debug mode")
    LOG_LEVEL: str = Field(default="INFO", description="Log level")
    
    # Redis Configuration
    REDIS_URL: str = Field(default="redis://localhost:6379/0", description="Redis URL")
    REDIS_HOST: str = Field(default="localhost", description="Redis host")
    REDIS_PORT: int = Field(default=6379, description="Redis port")
    REDIS_DB: int = Field(default=0, description="Redis database")
    
    # AWS/S3 Configuration
    AWS_ACCESS_KEY_ID: str = Field(default="test", description="AWS access key")
    AWS_SECRET_ACCESS_KEY: str = Field(default="test", description="AWS secret key")
    AWS_REGION: str = Field(default="us-east-1", description="AWS region")
    S3_ENDPOINT_URL: str = Field(default="http://localhost:4566", description="S3 endpoint URL")
    INPUT_BUCKET: str = Field(default="video-uploads", description="Input S3 bucket")
    OUTPUT_BUCKET: str = Field(default="video-highlights", description="Output S3 bucket")
    
    # Worker Configuration
    WORKER_QUEUE_NAME: str = Field(default="video_processing", description="Worker queue name")
    WORKER_CONCURRENCY: int = Field(default=2, description="Worker concurrency")
    JOB_TIMEOUT: int = Field(default=3600, description="Job timeout in seconds")
    
    # Video Processing Configuration
    WHISPER_MODEL: str = Field(default="medium", description="Whisper model size")
    TEMP_DIR: str = Field(default="/tmp/video_processing", description="Temporary directory")
    
    # Highlight Detection Configuration
    HIGHLIGHT_PHRASES: str = Field(
        default='{"double kill": [-3, 2], "triple kill": [-3, 2], "maniac": [-4, 2], "savage": [-5, 3], "shutdown": [-2, 2]}',
        description="Highlight phrases and timing"
    )
    MERGE_GAP_THRESHOLD: float = Field(default=1.0, description="Gap threshold for merging clips")
    
    @validator('HIGHLIGHT_PHRASES')
    def parse_highlight_phrases(cls, v):
        """Parse highlight phrases from JSON string."""
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON format for HIGHLIGHT_PHRASES")
        return v
    
    # FFmpeg Configuration
    FFMPEG_THREADS: int = Field(default=2, description="FFmpeg thread count")
    VIDEO_CODEC: str = Field(default="copy", description="Video codec for clips")
    AUDIO_CODEC: str = Field(default="copy", description="Audio codec for clips")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
