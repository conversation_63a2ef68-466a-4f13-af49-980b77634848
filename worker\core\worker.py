"""
Main video processing worker implementation.
"""

import asyncio
import logging
import os
import tempfile
from datetime import datetime
from typing import Dict, Any, Optional, List

import redis.asyncio as redis
from core.config import get_settings
from processors.video_processor import VideoProcessor
from utils.redis_client import RedisJobManager
from utils.s3_client import S3Manager

logger = logging.getLogger(__name__)
settings = get_settings()


class VideoProcessingWorker:
    """Main worker class for processing video highlight extraction jobs."""
    
    def __init__(self):
        self.settings = settings
        self.redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
        self.job_manager = RedisJobManager(self.redis_client)
        self.s3_manager = S3Manager()
        self.video_processor = VideoProcessor()
        self.is_running = False
        self.worker_tasks: List[asyncio.Task] = []
    
    async def start(self):
        """Start the worker and begin processing jobs."""
        logger.info("Starting video processing worker")
        
        # Test connections
        try:
            await self.redis_client.ping()
            logger.info("Redis connection established")
        except Exception as e:
            logger.error(f"Redis connection failed: {e}")
            raise
        
        try:
            self.s3_manager.ensure_buckets_exist()
            logger.info("S3 connection established")
        except Exception as e:
            logger.error(f"S3 connection failed: {e}")
            raise
        
        # Initialize video processor
        try:
            await self.video_processor.initialize()
            logger.info("Video processor initialized")
        except Exception as e:
            logger.error(f"Video processor initialization failed: {e}")
            raise
        
        self.is_running = True
        
        # Start worker tasks
        for i in range(settings.WORKER_CONCURRENCY):
            task = asyncio.create_task(self._worker_loop(f"worker-{i}"))
            self.worker_tasks.append(task)
        
        logger.info(f"Started {settings.WORKER_CONCURRENCY} worker tasks")
    
    async def stop(self):
        """Stop the worker gracefully."""
        logger.info("Stopping video processing worker")
        self.is_running = False
        
        # Cancel all worker tasks
        for task in self.worker_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.worker_tasks:
            await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        
        # Close connections
        await self.redis_client.close()
        
        logger.info("Video processing worker stopped")
    
    async def _worker_loop(self, worker_name: str):
        """Main worker loop for processing jobs."""
        logger.info(f"Worker {worker_name} started")
        
        while self.is_running:
            try:
                # Get job from queue (blocking with timeout)
                job_id = await self.redis_client.brpop(
                    settings.WORKER_QUEUE_NAME, 
                    timeout=5
                )
                
                if not job_id:
                    continue  # Timeout, check if still running
                
                job_id = job_id[1]  # brpop returns (queue_name, value)
                logger.info(f"Worker {worker_name} processing job {job_id}")
                
                # Process the job
                await self._process_job(job_id, worker_name)
                
            except asyncio.CancelledError:
                logger.info(f"Worker {worker_name} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {e}", exc_info=True)
                await asyncio.sleep(1)  # Brief pause before retrying
        
        logger.info(f"Worker {worker_name} stopped")
    
    async def _process_job(self, job_id: str, worker_name: str):
        """Process a single video job."""
        start_time = datetime.utcnow()
        temp_dir = None
        
        try:
            # Get job data
            job_data = await self.job_manager.get_job(job_id)
            if not job_data:
                logger.error(f"Job {job_id} not found")
                return
            
            # Update status to processing
            await self.job_manager.update_job_status(
                job_id=job_id,
                status="processing",
                progress=20,
                message="Starting video processing",
                worker=worker_name,
                updated_at=datetime.utcnow().isoformat()
            )
            
            # Create temporary directory for this job
            temp_dir = tempfile.mkdtemp(
                prefix=f"job_{job_id}_",
                dir=settings.TEMP_DIR
            )
            logger.info(f"Created temp directory: {temp_dir}")
            
            # Step 1: Download video from S3
            await self.job_manager.update_job_status(
                job_id=job_id,
                status="processing",
                progress=30,
                message="Downloading video from S3"
            )
            
            input_video_path = await self._download_video(job_data, temp_dir)
            
            # Step 2: Extract audio and run Whisper
            await self.job_manager.update_job_status(
                job_id=job_id,
                status="processing",
                progress=50,
                message="Analyzing audio with Whisper AI"
            )
            
            transcript = await self.video_processor.extract_transcript(input_video_path)
            
            # Step 3: Detect highlights
            await self.job_manager.update_job_status(
                job_id=job_id,
                status="processing",
                progress=70,
                message="Detecting highlight moments"
            )
            
            highlight_ranges = await self.video_processor.detect_highlights(transcript)
            
            if not highlight_ranges:
                await self.job_manager.complete_job(job_id, [])
                logger.info(f"Job {job_id} completed with no highlights found")
                return
            
            # Step 4: Create highlight clips
            await self.job_manager.update_job_status(
                job_id=job_id,
                status="processing",
                progress=80,
                message=f"Creating {len(highlight_ranges)} highlight clips"
            )
            
            clip_paths = await self.video_processor.create_clips(
                input_video_path, 
                highlight_ranges, 
                temp_dir
            )
            
            # Step 5: Upload clips to S3
            await self.job_manager.update_job_status(
                job_id=job_id,
                status="processing",
                progress=90,
                message="Uploading highlight clips"
            )
            
            highlight_urls = await self._upload_clips(job_id, clip_paths)
            
            # Complete job
            await self.job_manager.complete_job(job_id, highlight_urls)
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            logger.info(f"Job {job_id} completed successfully in {processing_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Job {job_id} failed: {e}", exc_info=True)
            await self.job_manager.fail_job(
                job_id=job_id,
                error_message=str(e),
                error_details={"worker": worker_name, "error_type": type(e).__name__}
            )
        finally:
            # Cleanup temporary directory
            if temp_dir and os.path.exists(temp_dir):
                try:
                    import shutil
                    shutil.rmtree(temp_dir)
                    logger.info(f"Cleaned up temp directory: {temp_dir}")
                except Exception as e:
                    logger.warning(f"Failed to cleanup temp directory {temp_dir}: {e}")
    
    async def _download_video(self, job_data: Dict[str, Any], temp_dir: str) -> str:
        """Download video file from S3 to local temporary storage."""
        object_key = job_data["object_key"]
        filename = job_data["filename"]
        
        # Create local file path
        local_path = os.path.join(temp_dir, filename)
        
        # Download from S3
        self.s3_manager.download_file(
            bucket=settings.INPUT_BUCKET,
            object_key=object_key,
            local_path=local_path
        )
        
        logger.info(f"Downloaded video to {local_path}")
        return local_path
    
    async def _upload_clips(self, job_id: str, clip_paths: List[str]) -> List[str]:
        """Upload highlight clips to S3 and return their keys."""
        highlight_keys = []
        
        for i, clip_path in enumerate(clip_paths):
            # Create S3 key for the clip
            clip_filename = f"clip_{i+1:02d}.mp4"
            object_key = f"highlights/{job_id}/{clip_filename}"
            
            # Upload to S3
            self.s3_manager.upload_file(
                local_path=clip_path,
                bucket=settings.OUTPUT_BUCKET,
                object_key=object_key
            )
            
            highlight_keys.append(object_key)
            logger.info(f"Uploaded clip {i+1} to {object_key}")
        
        return highlight_keys
