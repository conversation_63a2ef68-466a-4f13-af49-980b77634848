# Development override for docker-compose.yml
# Use LocalStack instead of Backblaze for local development
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

version: '3.8'

services:
  # Override API service for development
  api:
    environment:
      - REDIS_URL=redis://redis:6379/0
      - BACKBLAZE_ACCESS_KEY_ID=test
      - BACKBLAZE_SECRET_ACCESS_KEY=test
      - BACKBLAZE_REGION=us-east-1
      - BACKBLAZE_ENDPOINT_URL=http://localstack:4566
      - INPUT_BUCKET=video-uploads
      - OUTPUT_BUCKET=video-highlights
      - FRONTEND_URL=http://localhost:3000
      - DEBUG=true

  # Override worker service for development
  worker:
    environment:
      - REDIS_URL=redis://redis:6379/0
      - BACKBLAZE_ACCESS_KEY_ID=test
      - BACKBLAZE_SECRET_ACCESS_KEY=test
      - BACKBLAZE_REGION=us-east-1
      - BACKBLAZE_ENDPOINT_URL=http://localstack:4566
      - INPUT_BUCKET=video-uploads
      - OUTPUT_BUCKET=video-highlights
      - WHISPER_MODEL=tiny  # Use smaller model for faster development
      - TEMP_DIR=/tmp/video_processing
      - DEBUG=true

  # LocalStack for S3 simulation (only in development)
  localstack:
    image: localstack/localstack:latest
    ports:
      - "4566:4566"
    environment:
      - SERVICES=s3
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - localstack_data:/tmp/localstack
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - app-network

  # Redis Commander for development debugging
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - app-network

volumes:
  localstack_data:
