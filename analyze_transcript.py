#!/usr/bin/env python3
"""
Analyze the transcript to see what was actually detected and why other events were missed
"""

import os
import sys
import asyncio
import re
from dotenv import load_dotenv

# Load environment
load_dotenv('.env')

# Import our exact processor
from exact_video_processor import ExactVideoProcessor

async def analyze_transcript():
    """Analyze the full transcript to understand what was detected"""
    print("🔍 Analyzing Transcript for Gaming Events")
    print("=" * 60)
    
    video_path = "backend/samples/24 Kills + MANIAC!! Alucard Best Build Exp Lane!! - Build Top 1 Global Alucard ~ MLBB.mp4"
    
    if not os.path.exists(video_path):
        print("❌ Sample video not found!")
        return False
    
    try:
        # Initialize processor
        processor = ExactVideoProcessor()
        await processor.initialize()
        
        # Extract transcript
        print("🔊 Extracting full transcript...")
        transcript = await processor.extract_transcript(video_path)
        segments = transcript.get('segments', [])
        
        print(f"✅ Transcript extracted: {len(segments)} segments")
        
        # Analyze full transcript text
        full_text = ""
        all_words = []
        
        for segment in segments:
            segment_text = segment.get('text', '').strip()
            full_text += " " + segment_text
            
            # Collect all words with timestamps
            words = segment.get('words', [])
            for word_info in words:
                word_text = word_info.get('word', '').strip()
                start_time = word_info.get('start', 0)
                all_words.append({
                    'text': word_text,
                    'start': start_time,
                    'clean': re.sub(r'[^\w\s]', '', word_text.lower())
                })
        
        print(f"\n📝 FULL TRANSCRIPT TEXT:")
        print("=" * 60)
        print(full_text.strip())
        print("=" * 60)
        
        # Look for gaming-related keywords
        print(f"\n🎮 GAMING KEYWORD ANALYSIS:")
        gaming_keywords = [
            'kill', 'blood', 'enemy', 'slain', 'double', 'triple', 'mega', 
            'maniac', 'savage', 'godlike', 'legendary', 'spree', 'first',
            'monster', 'unstoppable', 'rampage', 'killing'
        ]
        
        found_keywords = {}
        for keyword in gaming_keywords:
            # Case-insensitive search
            pattern = re.compile(r'\b' + re.escape(keyword) + r'\b', re.IGNORECASE)
            matches = pattern.findall(full_text)
            if matches:
                found_keywords[keyword] = len(matches)
                print(f"   ✅ '{keyword}': {len(matches)} times")
        
        if not found_keywords:
            print("   ❌ No gaming keywords found in transcript")
        
        # Check for our exact phrases
        print(f"\n🎯 EXACT PHRASE ANALYSIS:")
        for event_name, phrase_variations in processor.exact_phrases.items():
            print(f"\n{event_name}:")
            found_any = False
            
            for phrase in phrase_variations:
                # Case-insensitive search for exact phrase
                pattern = re.compile(re.escape(phrase), re.IGNORECASE)
                matches = pattern.findall(full_text)
                
                if matches:
                    print(f"   ✅ FOUND: '{phrase}' ({len(matches)} times)")
                    found_any = True
                    
                    # Find timestamps
                    phrase_words = phrase.lower().split()
                    for i in range(len(all_words) - len(phrase_words) + 1):
                        word_sequence = []
                        for j in range(len(phrase_words)):
                            if i + j < len(all_words):
                                word_sequence.append(all_words[i + j]['clean'])
                        
                        if word_sequence == phrase_words:
                            timestamp = all_words[i]['start']
                            print(f"      → At {timestamp:.1f}s")
                else:
                    print(f"   ❌ NOT FOUND: '{phrase}'")
            
            if not found_any:
                # Look for partial matches
                phrase_words = phrase_variations[0].lower().split()
                partial_matches = []
                
                for word in phrase_words:
                    if word in full_text.lower():
                        partial_matches.append(word)
                
                if partial_matches:
                    print(f"   ⚠️  PARTIAL: Found words {partial_matches}")
                else:
                    print(f"   ❌ NO MATCH: No words from '{phrase_variations[0]}' found")
        
        # Look for alternative phrasings
        print(f"\n🔍 ALTERNATIVE PHRASING ANALYSIS:")
        alternative_patterns = {
            'double_kill': [r'double.*kill', r'two.*kill', r'2.*kill'],
            'triple_kill': [r'triple.*kill', r'three.*kill', r'3.*kill'],
            'mega_kill': [r'mega.*kill', r'ultra.*kill'],
            'maniac': [r'maniac', r'rampage', r'killing.*spree'],
            'savage': [r'savage', r'beyond.*godlike'],
            'first_blood': [r'first.*blood', r'first.*kill'],
            'you_have_slain_an_enemy': [r'slain.*enemy', r'killed.*enemy', r'enemy.*down']
        }
        
        for event, patterns in alternative_patterns.items():
            print(f"\n{event} alternatives:")
            for pattern in patterns:
                matches = re.findall(pattern, full_text, re.IGNORECASE)
                if matches:
                    print(f"   ✅ Pattern '{pattern}': {matches}")
                else:
                    print(f"   ❌ Pattern '{pattern}': No matches")
        
        # Show word-by-word analysis around timestamps
        print(f"\n📊 WORD-BY-WORD ANALYSIS:")
        print("First 50 words with timestamps:")
        for i, word_info in enumerate(all_words[:50]):
            print(f"   {i:2d}. [{word_info['start']:6.1f}s] '{word_info['text']}' (clean: '{word_info['clean']}')")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing transcript: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run transcript analysis"""
    print("🚀 Transcript Analysis for Gaming Event Detection")
    print("Understanding why events were missed")
    
    success = asyncio.run(analyze_transcript())
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 ANALYSIS COMPLETE!")
        print("\n💡 This analysis will help us understand:")
        print("   • What was actually transcribed")
        print("   • Why exact matches failed")
        print("   • What alternative phrasings exist")
        print("   • How to improve detection accuracy")
    else:
        print("\n❌ Analysis failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
