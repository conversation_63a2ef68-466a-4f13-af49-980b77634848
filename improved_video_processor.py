"""
Improved video processor with better Whisper model for accurate gaming transcription
"""

import asyncio
import logging
import os
import re
from typing import List, Dict, Any, Tuple

import whisper
import ffmpeg
from dotenv import load_dotenv

# Load environment
load_dotenv('.env')

# Add paths
import sys
sys.path.append('worker')
from core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class ImprovedVideoProcessor:
    """Video processor with better Whisper model for accurate gaming transcription."""
    
    def __init__(self):
        self.whisper_model = None
        self.highlight_phrases = settings.HIGHLIGHT_PHRASES
        self.multi_kill_sequences = settings.MULTI_KILL_SEQUENCES
        self.merge_gap_threshold = settings.MERGE_GAP_THRESHOLD
        
        # Define flexible phrases to match (case-insensitive with variations)
        self.gaming_phrases = {
            "double_kill": [
                "double kill", "double-kill", "doublekill", 
                "two kills", "2 kills", "double elimination"
            ],
            "first_blood": [
                "first blood", "first-blood", "firstblood",
                "first kill", "first elimination"
            ],
            "triple_kill": [
                "triple kill", "triple-kill", "triplekill",
                "three kills", "3 kills", "triple elimination"
            ],
            "mega_kill": [
                "mega kill", "mega-kill", "megakill",
                "ultra kill", "ultra-kill", "ultrakill"
            ],
            "monster_kill": [
                "monster kill", "monster-kill", "monsterkill"
            ],
            "maniac": [
                "maniac", "rampage", "killing spree", "on a rampage"
            ],
            "savage": [
                "savage", "beyond godlike", "legendary kill"
            ],
            "godlike": [
                "godlike", "god-like", "god like", "dominating"
            ],
            "legendary": [
                "legendary", "legendary kill", "legendary spree"
            ],
            "unstoppable": [
                "unstoppable", "unstoppable force", "can't be stopped"
            ],
            "killing_spree": [
                "killing spree", "killing-spree", "killingspree",
                "on a spree", "spree"
            ],
            "you_have_slain_an_enemy": [
                "you have slain an enemy", "enemy slain", "slain an enemy",
                "you killed an enemy", "enemy eliminated", "enemy down",
                "you have killed", "target eliminated"
            ]
        }
    
    async def initialize(self, model_size="base"):
        """Initialize with better Whisper model."""
        logger.info(f"Loading Whisper model: {model_size}")
        print(f"🔊 Loading Whisper '{model_size}' model for better accuracy...")
        
        # Try to load the model
        try:
            loop = asyncio.get_event_loop()
            self.whisper_model = await loop.run_in_executor(
                None, 
                whisper.load_model, 
                model_size
            )
            print(f"✅ Whisper '{model_size}' model loaded successfully")
            logger.info(f"Whisper {model_size} model loaded successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to load '{model_size}' model: {e}")
            
            # Fallback to smaller model
            if model_size != "tiny":
                print(f"🔄 Falling back to 'tiny' model...")
                return await self.initialize("tiny")
            else:
                raise e
    
    async def extract_transcript_improved(self, video_path: str) -> Dict[str, Any]:
        """Extract transcript with improved settings for gaming audio."""
        logger.info(f"Extracting transcript from {video_path}")
        print(f"🎮 Extracting transcript optimized for gaming audio...")
        
        if not self.whisper_model:
            raise RuntimeError("Whisper model not initialized")
        
        # Run Whisper transcription with gaming-optimized settings
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            lambda: self.whisper_model.transcribe(
                video_path,
                word_timestamps=True,
                verbose=True,
                language="en",  # Force English
                temperature=0.0,  # More deterministic
                best_of=5,  # Try multiple attempts
                beam_size=5,  # Better beam search
                patience=1.0,  # More patient decoding
                condition_on_previous_text=True,  # Use context
                initial_prompt="Gaming video with announcements like double kill, triple kill, maniac, first blood, enemy slain"  # Gaming context
            )
        )
        
        logger.info(f"Transcript extracted: {len(result.get('segments', []))} segments")
        print(f"✅ Transcript extracted: {len(result.get('segments', []))} segments")
        return result
    
    async def detect_highlights_flexible(self, transcript: Dict[str, Any]) -> List[Tuple[float, float, str]]:
        """
        Detect highlight moments with flexible phrase matching.
        """
        logger.info("Detecting highlights with flexible phrase matching")
        print(f"🎯 Detecting gaming events with flexible matching...")
        
        segments = transcript.get("segments", [])
        if not segments:
            logger.warning("No segments found in transcript")
            return []
        
        # Show full transcript for analysis
        print(f"\n📝 Full Transcript Analysis:")
        full_text = ""
        for i, segment in enumerate(segments):
            text = segment.get('text', '').strip()
            start = segment.get('start', 0)
            end = segment.get('end', 0)
            full_text += " " + text
            print(f"   [{start:6.1f}s-{end:6.1f}s]: {text}")
        
        print(f"\n🔍 Full Text: {full_text.strip()}")
        
        # Extract all text with word-level timestamps
        all_words_with_times = []
        for segment in segments:
            words = segment.get("words", [])
            for word_info in words:
                word_text = word_info.get("word", "").strip()
                start_time = word_info.get("start", 0)
                end_time = word_info.get("end", 0)
                all_words_with_times.append({
                    "text": word_text,
                    "start": start_time,
                    "end": end_time,
                    "clean": re.sub(r'[^\w\s]', '', word_text.lower())
                })
        
        # Find flexible phrase matches
        flexible_matches = []
        
        print(f"\n🎮 Searching for Gaming Events:")
        for event_name, phrase_variations in self.gaming_phrases.items():
            print(f"\n{event_name}:")
            
            for phrase in phrase_variations:
                matches = self._find_flexible_phrase_matches(all_words_with_times, phrase, full_text)
                for match_time in matches:
                    flexible_matches.append({
                        'event': event_name,
                        'phrase': phrase,
                        'time': match_time,
                        'config': self.highlight_phrases[event_name]
                    })
                    print(f"   ✅ FOUND: '{phrase}' at {match_time:.2f}s")
        
        if not flexible_matches:
            print(f"   ❌ No gaming events detected")
            print(f"\n💡 Possible reasons:")
            print(f"   • Gaming events use different phrasing")
            print(f"   • Audio quality issues")
            print(f"   • Background noise interference")
            print(f"   • Non-English announcements")
            return []
        
        # Sort matches by time
        flexible_matches.sort(key=lambda x: x['time'])
        
        print(f"\n🎬 Creating highlight clips...")
        
        # Create highlight ranges
        highlight_ranges = []
        processed_matches = set()
        
        for i, match in enumerate(flexible_matches):
            if i in processed_matches:
                continue
            
            event_name = match['event']
            match_time = match['time']
            config = match['config']
            start_offset, end_offset, is_sequence = config
            
            # Check if this is a sequence event
            if is_sequence and event_name in self.multi_kill_sequences:
                sequence_range = self._process_flexible_sequence(
                    event_name, match, flexible_matches, i, processed_matches
                )
                if sequence_range:
                    highlight_ranges.append(sequence_range)
            else:
                # Single event highlight
                clip_start = max(0, match_time + start_offset)
                clip_end = match_time + end_offset
                highlight_ranges.append((clip_start, clip_end, event_name))
                processed_matches.add(i)
                print(f"   📹 {event_name}: {clip_start:.1f}s - {clip_end:.1f}s")
        
        # Merge overlapping ranges
        merged_ranges = self._merge_overlapping_ranges_with_events(highlight_ranges)
        
        print(f"\n✅ Detected {len(merged_ranges)} highlight clips total")
        return merged_ranges
    
    def _find_flexible_phrase_matches(self, words_with_times: List[Dict], target_phrase: str, full_text: str) -> List[float]:
        """Find flexible phrase matches using multiple strategies."""
        matches = []
        target_phrase_lower = target_phrase.lower()
        
        # Strategy 1: Exact phrase match in full text
        if target_phrase_lower in full_text.lower():
            # Find word-level timestamps for this phrase
            target_words = target_phrase_lower.split()
            
            for i in range(len(words_with_times) - len(target_words) + 1):
                word_sequence = []
                for j in range(len(target_words)):
                    if i + j < len(words_with_times):
                        word_text = words_with_times[i + j]["clean"]
                        word_sequence.append(word_text)
                
                # Check for exact match
                if len(word_sequence) == len(target_words):
                    sequence_text = " ".join(word_sequence)
                    if sequence_text == " ".join(target_words):
                        match_time = words_with_times[i]["start"]
                        matches.append(match_time)
        
        # Strategy 2: Fuzzy matching for common variations
        # Handle hyphenated words, spacing issues, etc.
        normalized_phrase = re.sub(r'[-\s]+', '', target_phrase_lower)
        normalized_text = re.sub(r'[-\s]+', '', full_text.lower())
        
        if normalized_phrase in normalized_text:
            # Find approximate timestamp
            words_text = " ".join([w["clean"] for w in words_with_times])
            if normalized_phrase in re.sub(r'[-\s]+', '', words_text):
                # Use middle of transcript as approximation
                if words_with_times:
                    middle_time = words_with_times[len(words_with_times)//2]["start"]
                    matches.append(middle_time)
        
        return matches
    
    def _process_flexible_sequence(
        self, 
        target_event: str, 
        target_match: Dict, 
        all_matches: List[Dict], 
        target_index: int,
        processed_matches: set
    ) -> Tuple[float, float, str]:
        """Process multi-kill sequences with flexible matching."""
        # For now, treat as single event but with longer duration
        config = target_match['config']
        start_offset, end_offset, _ = config
        
        # Extend duration for sequence events
        if target_event in ['maniac', 'savage', 'godlike', 'unstoppable']:
            start_offset = min(start_offset, -15)  # Start earlier
            end_offset = max(end_offset, 8)  # End later
        
        match_time = target_match['time']
        clip_start = max(0, match_time + start_offset)
        clip_end = match_time + end_offset
        processed_matches.add(target_index)
        
        return (clip_start, clip_end, f"{target_event}_sequence")
    
    def _merge_overlapping_ranges_with_events(self, ranges: List[Tuple[float, float, str]]) -> List[Tuple[float, float, str]]:
        """Merge overlapping ranges while preserving event names."""
        if not ranges:
            return []
        
        # Sort by start time
        sorted_ranges = sorted(ranges, key=lambda x: x[0])
        merged = [sorted_ranges[0]]
        
        for current_start, current_end, current_event in sorted_ranges[1:]:
            last_start, last_end, last_event = merged[-1]
            
            # Check if ranges overlap or are close (within 3 seconds)
            if current_start <= last_end + 3.0:
                # Merge ranges, combine event names
                new_end = max(last_end, current_end)
                combined_event = f"{last_event}+{current_event}" if last_event != current_event else last_event
                merged[-1] = (last_start, new_end, combined_event)
            else:
                merged.append((current_start, current_end, current_event))
        
        return merged
    
    async def create_clips_with_names(
        self, 
        input_video_path: str, 
        highlight_ranges: List[Tuple[float, float, str]], 
        output_dir: str
    ) -> List[str]:
        """Create highlight clips with event-specific names."""
        logger.info(f"Creating {len(highlight_ranges)} clips with event names")
        print(f"🎞️  Creating {len(highlight_ranges)} video clips...")
        
        clip_paths = []
        
        for i, (start_time, end_time, event_name) in enumerate(highlight_ranges):
            # Create filename with event name and timestamp
            clip_filename = f"{event_name}_{start_time:.1f}s-{end_time:.1f}s.mp4"
            clip_path = os.path.join(output_dir, clip_filename)
            
            duration = end_time - start_time
            
            try:
                await self._extract_clip_ffmpeg(input_video_path, clip_path, start_time, duration)
                clip_paths.append(clip_path)
                print(f"   ✅ {clip_filename}")
                
            except Exception as e:
                print(f"   ❌ Failed: {clip_filename} - {e}")
                continue
        
        return clip_paths
    
    async def _extract_clip_ffmpeg(self, input_path: str, output_path: str, start_time: float, duration: float):
        """Extract a video clip using FFmpeg."""
        stream = ffmpeg.input(input_path, ss=start_time, t=duration)
        stream = ffmpeg.output(
            stream,
            output_path,
            vcodec='copy',
            acodec='copy',
            avoid_negative_ts='make_zero'
        )
        
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            None,
            lambda: ffmpeg.run(stream, overwrite_output=True, quiet=True)
        )
