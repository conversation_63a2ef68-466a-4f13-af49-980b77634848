"""
FastAPI main application module.
Handles video upload callbacks and job status endpoints.
"""

import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse

from app.core.config import get_settings
from app.core.redis_client import get_redis_client
from app.core.s3_client import get_s3_client
from app.api.routes import upload, status, health
from app.core.logging import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    logger.info("Starting Video Highlight Extractor API")
    
    # Initialize services
    redis_client = get_redis_client()
    s3_client = get_s3_client()
    
    # Test connections
    try:
        await redis_client.ping()
        logger.info("Redis connection established")
    except Exception as e:
        logger.error(f"Redis connection failed: {e}")
    
    try:
        # Test S3 connection by listing buckets
        s3_client.list_buckets()
        logger.info("S3 connection established")
    except Exception as e:
        logger.error(f"S3 connection failed: {e}")
    
    yield
    
    logger.info("Shutting down Video Highlight Extractor API")


# Create FastAPI application
app = FastAPI(
    title="Video Highlight Extractor API",
    description="AI-powered gaming highlight extraction from uploaded videos",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


# Include routers
app.include_router(health.router, prefix="", tags=["Health"])
app.include_router(upload.router, prefix="/upload", tags=["Upload"])
app.include_router(status.router, prefix="/status", tags=["Status"])


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Video Highlight Extractor API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
    }


@app.get("/info")
async def info():
    """Get API information and configuration."""
    return {
        "app_name": "Video Highlight Extractor",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "redis_connected": True,  # This would be checked in real implementation
        "s3_configured": True,    # This would be checked in real implementation
        "supported_formats": ["mp4", "avi", "mov", "mkv", "webm"],
        "max_file_size": "500MB",
        "highlight_phrases": [
            "double kill",
            "triple kill", 
            "maniac",
            "savage",
            "shutdown"
        ]
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )
