#!/bin/bash

# Initialize LocalStack S3 buckets for development
# This script runs after LocalStack starts up

echo "Initializing LocalStack S3 buckets..."

# Wait for LocalStack to be ready
echo "Waiting for LocalStack to be ready..."
until curl -s http://localstack:4566/_localstack/health | grep -q '"s3": "available"'; do
    echo "Waiting for S3 service..."
    sleep 2
done

echo "LocalStack S3 is ready!"

# Create buckets
echo "Creating S3 buckets..."

aws --endpoint-url=http://localstack:4566 s3 mb s3://video-uploads --region us-east-1
aws --endpoint-url=http://localstack:4566 s3 mb s3://video-highlights --region us-east-1

# Set bucket policies for public read access (development only)
echo "Setting bucket policies..."

# Policy for video-uploads bucket
cat > /tmp/upload-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::video-uploads/*"
        }
    ]
}
EOF

# Policy for video-highlights bucket
cat > /tmp/highlights-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::video-highlights/*"
        }
    ]
}
EOF

aws --endpoint-url=http://localstack:4566 s3api put-bucket-policy \
    --bucket video-uploads \
    --policy file:///tmp/upload-policy.json

aws --endpoint-url=http://localstack:4566 s3api put-bucket-policy \
    --bucket video-highlights \
    --policy file:///tmp/highlights-policy.json

# Enable CORS for uploads bucket
cat > /tmp/cors-config.json << EOF
{
    "CORSRules": [
        {
            "AllowedHeaders": ["*"],
            "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
            "AllowedOrigins": ["*"],
            "ExposeHeaders": ["ETag"],
            "MaxAgeSeconds": 3000
        }
    ]
}
EOF

aws --endpoint-url=http://localstack:4566 s3api put-bucket-cors \
    --bucket video-uploads \
    --cors-configuration file:///tmp/cors-config.json

aws --endpoint-url=http://localstack:4566 s3api put-bucket-cors \
    --bucket video-highlights \
    --cors-configuration file:///tmp/cors-config.json

echo "LocalStack S3 initialization complete!"

# List buckets to verify
echo "Created buckets:"
aws --endpoint-url=http://localstack:4566 s3 ls
