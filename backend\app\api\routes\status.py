"""
Job status endpoints.
"""

import logging
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from app.core.redis_client import get_job_manager
from app.core.s3_client import get_s3_manager
from app.core.config import get_settings

logger = logging.getLogger(__name__)
router = APIRouter()
settings = get_settings()


class JobStatusResponse(BaseModel):
    """Response model for job status."""
    job_id: str
    status: str  # pending, processing, completed, failed
    progress: int  # 0-100
    message: str
    created_at: str
    updated_at: str
    highlights: Optional[List[str]] = None
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    
    # Additional metadata
    filename: Optional[str] = None
    file_size: Optional[int] = None
    processing_time: Optional[float] = None


@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_job_status(
    job_id: str,
    job_manager: Any = Depends(get_job_manager),
    s3_manager: Any = Depends(get_s3_manager)
):
    """
    Get the current status and progress of a video processing job.
    
    Returns detailed information about the job including:
    - Current processing status
    - Progress percentage
    - List of highlight clip URLs (when completed)
    - Error information (if failed)
    """
    try:
        # Get job data from Redis
        job_data = await job_manager.get_job(job_id)
        
        if not job_data:
            raise HTTPException(
                status_code=404,
                detail=f"Job {job_id} not found or has expired"
            )
        
        # If job is completed, ensure highlight URLs are accessible
        highlights = None
        if job_data.get("status") == "completed" and job_data.get("highlights"):
            highlights = []
            for highlight_key in job_data["highlights"]:
                try:
                    # Generate presigned download URL for each highlight
                    download_url = s3_manager.generate_presigned_download_url(
                        bucket=settings.OUTPUT_BUCKET,
                        object_key=highlight_key,
                        expiration=3600  # 1 hour
                    )
                    highlights.append(download_url)
                except Exception as e:
                    logger.warning(f"Failed to generate download URL for {highlight_key}: {e}")
                    # Still include the S3 key as fallback
                    highlights.append(f"s3://{settings.OUTPUT_BUCKET}/{highlight_key}")
        
        # Calculate processing time if available
        processing_time = None
        if job_data.get("created_at") and job_data.get("updated_at"):
            try:
                from datetime import datetime
                created = datetime.fromisoformat(job_data["created_at"].replace('Z', '+00:00'))
                updated = datetime.fromisoformat(job_data["updated_at"].replace('Z', '+00:00'))
                processing_time = (updated - created).total_seconds()
            except:
                pass
        
        response = JobStatusResponse(
            job_id=job_id,
            status=job_data.get("status", "unknown"),
            progress=job_data.get("progress", 0),
            message=job_data.get("message", ""),
            created_at=job_data.get("created_at", ""),
            updated_at=job_data.get("updated_at", ""),
            highlights=highlights,
            error_message=job_data.get("error_message"),
            error_details=job_data.get("error_details"),
            filename=job_data.get("filename"),
            file_size=job_data.get("file_size"),
            processing_time=processing_time
        )
        
        logger.info(f"Retrieved status for job {job_id}: {response.status}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job status for {job_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve job status"
        )


@router.get("/{job_id}/highlights")
async def get_job_highlights(
    job_id: str,
    job_manager: Any = Depends(get_job_manager),
    s3_manager: Any = Depends(get_s3_manager)
):
    """
    Get only the highlight URLs for a completed job.
    
    This is a convenience endpoint for when you only need the final results.
    """
    try:
        job_data = await job_manager.get_job(job_id)
        
        if not job_data:
            raise HTTPException(
                status_code=404,
                detail=f"Job {job_id} not found"
            )
        
        if job_data.get("status") != "completed":
            raise HTTPException(
                status_code=400,
                detail=f"Job {job_id} is not completed yet (status: {job_data.get('status')})"
            )
        
        highlights = job_data.get("highlights", [])
        if not highlights:
            return {
                "job_id": job_id,
                "highlights": [],
                "message": "No highlights found in this video"
            }
        
        # Generate download URLs
        download_urls = []
        for highlight_key in highlights:
            try:
                download_url = s3_manager.generate_presigned_download_url(
                    bucket=settings.OUTPUT_BUCKET,
                    object_key=highlight_key,
                    expiration=3600
                )
                download_urls.append({
                    "url": download_url,
                    "filename": highlight_key.split('/')[-1],
                    "s3_key": highlight_key
                })
            except Exception as e:
                logger.warning(f"Failed to generate download URL for {highlight_key}: {e}")
        
        return {
            "job_id": job_id,
            "highlights": download_urls,
            "total_count": len(download_urls),
            "message": f"Found {len(download_urls)} highlight clips"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get highlights for job {job_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve job highlights"
        )


@router.delete("/{job_id}")
async def delete_job(
    job_id: str,
    job_manager: Any = Depends(get_job_manager),
    s3_manager: Any = Depends(get_s3_manager)
):
    """
    Delete a job and its associated files.
    
    This will remove:
    - Job data from Redis
    - Original uploaded video from S3
    - Generated highlight clips from S3
    """
    try:
        job_data = await job_manager.get_job(job_id)
        
        if not job_data:
            raise HTTPException(
                status_code=404,
                detail=f"Job {job_id} not found"
            )
        
        # Delete original file from input bucket
        if job_data.get("object_key"):
            try:
                s3_manager.delete_object(settings.INPUT_BUCKET, job_data["object_key"])
                logger.info(f"Deleted original file for job {job_id}")
            except Exception as e:
                logger.warning(f"Failed to delete original file for job {job_id}: {e}")
        
        # Delete highlight clips from output bucket
        if job_data.get("highlights"):
            for highlight_key in job_data["highlights"]:
                try:
                    s3_manager.delete_object(settings.OUTPUT_BUCKET, highlight_key)
                    logger.info(f"Deleted highlight {highlight_key}")
                except Exception as e:
                    logger.warning(f"Failed to delete highlight {highlight_key}: {e}")
        
        # Delete job data from Redis
        redis_client = job_manager.redis
        job_key = f"{job_manager.job_prefix}{job_id}"
        await redis_client.delete(job_key)
        
        logger.info(f"Deleted job {job_id}")
        
        return {
            "status": "success",
            "job_id": job_id,
            "message": "Job and associated files deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete job {job_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete job"
        )
