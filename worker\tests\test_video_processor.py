"""
Tests for video processor.
"""

import pytest
import os
from unittest.mock import patch, MagicMock, AsyncMock

from processors.video_processor import VideoProcessor


@pytest.mark.asyncio
async def test_video_processor_initialization():
    """Test video processor initialization."""
    processor = VideoProcessor()
    
    with patch('whisper.load_model') as mock_load_model:
        mock_model = MagicMock()
        mock_load_model.return_value = mock_model
        
        await processor.initialize()
        
        assert processor.whisper_model == mock_model
        mock_load_model.assert_called_once_with("medium")


@pytest.mark.asyncio
async def test_extract_transcript(sample_video_file, mock_whisper_model):
    """Test transcript extraction from video."""
    processor = VideoProcessor()
    processor.whisper_model = mock_whisper_model
    
    transcript = await processor.extract_transcript(sample_video_file)
    
    assert "segments" in transcript
    assert len(transcript["segments"]) == 2
    mock_whisper_model.transcribe.assert_called_once_with(
        sample_video_file,
        word_timestamps=True,
        verbose=False
    )


@pytest.mark.asyncio
async def test_detect_highlights(sample_transcript):
    """Test highlight detection in transcript."""
    processor = VideoProcessor()
    
    highlight_ranges = await processor.detect_highlights(sample_transcript)
    
    # Should find highlights for "double kill" and "triple kill"
    assert len(highlight_ranges) >= 1
    
    # Check that ranges are tuples of (start, end)
    for start, end in highlight_ranges:
        assert isinstance(start, (int, float))
        assert isinstance(end, (int, float))
        assert start < end


def test_find_phrase_matches():
    """Test phrase matching in word list."""
    processor = VideoProcessor()
    
    words = [
        {"word": "Player", "start": 0.0, "end": 0.5},
        {"word": "got", "start": 0.5, "end": 0.8},
        {"word": "double", "start": 1.0, "end": 1.5},
        {"word": "kill", "start": 1.5, "end": 2.0},
    ]
    
    # Test single word match
    matches = processor._find_phrase_matches(words, "kill")
    assert len(matches) == 1
    assert matches[0] == 1.5
    
    # Test multi-word phrase match
    matches = processor._find_phrase_matches(words, "double kill")
    assert len(matches) == 1
    assert matches[0] == 1.0


def test_merge_overlapping_ranges():
    """Test merging of overlapping highlight ranges."""
    processor = VideoProcessor()
    
    # Test overlapping ranges
    ranges = [(1.0, 5.0), (4.0, 8.0), (10.0, 15.0)]
    merged = processor._merge_overlapping_ranges(ranges)
    
    assert len(merged) == 2
    assert merged[0] == (1.0, 8.0)  # First two merged
    assert merged[1] == (10.0, 15.0)  # Third separate
    
    # Test ranges close enough to merge
    ranges = [(1.0, 3.0), (3.5, 6.0)]  # Gap of 0.5s, threshold is 1.0s
    merged = processor._merge_overlapping_ranges(ranges)
    
    assert len(merged) == 1
    assert merged[0] == (1.0, 6.0)


@pytest.mark.asyncio
async def test_create_clips(temp_dir, sample_video_file, mock_ffmpeg):
    """Test video clip creation."""
    processor = VideoProcessor()
    
    highlight_ranges = [(1.0, 5.0), (10.0, 15.0)]
    
    clip_paths = await processor.create_clips(
        sample_video_file,
        highlight_ranges,
        temp_dir
    )
    
    assert len(clip_paths) == 2
    assert all(path.endswith('.mp4') for path in clip_paths)
    assert all(os.path.dirname(path) == temp_dir for path in clip_paths)
    
    # Verify FFmpeg was called correctly
    assert mock_ffmpeg["run"].call_count == 2


@pytest.mark.asyncio
async def test_extract_clip_ffmpeg(temp_dir, sample_video_file, mock_ffmpeg):
    """Test individual clip extraction with FFmpeg."""
    processor = VideoProcessor()
    
    output_path = os.path.join(temp_dir, "test_clip.mp4")
    
    await processor._extract_clip_ffmpeg(
        sample_video_file,
        output_path,
        start_time=1.0,
        duration=4.0
    )
    
    # Verify FFmpeg input was called with correct parameters
    mock_ffmpeg["input"].assert_called_once_with(sample_video_file, ss=1.0, t=4.0)
    
    # Verify FFmpeg output was called
    mock_ffmpeg["output"].assert_called_once()
    
    # Verify FFmpeg run was called
    mock_ffmpeg["run"].assert_called_once()


@pytest.mark.asyncio
async def test_get_video_info(sample_video_file, mock_ffmpeg):
    """Test video information extraction."""
    processor = VideoProcessor()
    
    video_info = await processor.get_video_info(sample_video_file)
    
    assert "duration" in video_info
    assert "width" in video_info
    assert "height" in video_info
    assert "fps" in video_info
    assert "codec" in video_info
    assert "size" in video_info
    
    assert video_info["duration"] == 30.0
    assert video_info["width"] == 1920
    assert video_info["height"] == 1080
    
    mock_ffmpeg["probe"].assert_called_once_with(sample_video_file)


@pytest.mark.asyncio
async def test_extract_transcript_no_model():
    """Test transcript extraction without initialized model."""
    processor = VideoProcessor()
    
    with pytest.raises(RuntimeError, match="Whisper model not initialized"):
        await processor.extract_transcript("dummy_path.mp4")


@pytest.mark.asyncio
async def test_detect_highlights_empty_transcript():
    """Test highlight detection with empty transcript."""
    processor = VideoProcessor()
    
    empty_transcript = {"segments": []}
    
    highlight_ranges = await processor.detect_highlights(empty_transcript)
    
    assert len(highlight_ranges) == 0


@pytest.mark.asyncio
async def test_detect_highlights_no_matches():
    """Test highlight detection with no matching phrases."""
    processor = VideoProcessor()
    
    transcript_no_matches = {
        "segments": [
            {
                "words": [
                    {"word": "This", "start": 0.0, "end": 0.5},
                    {"word": "is", "start": 0.5, "end": 0.7},
                    {"word": "boring", "start": 0.7, "end": 1.2},
                    {"word": "gameplay", "start": 1.2, "end": 2.0}
                ]
            }
        ]
    }
    
    highlight_ranges = await processor.detect_highlights(transcript_no_matches)
    
    assert len(highlight_ranges) == 0


@pytest.mark.asyncio
async def test_create_clips_ffmpeg_error(temp_dir, sample_video_file, mock_ffmpeg):
    """Test clip creation when FFmpeg fails."""
    processor = VideoProcessor()
    
    # Make FFmpeg run raise an exception
    mock_ffmpeg["run"].side_effect = Exception("FFmpeg error")
    
    highlight_ranges = [(1.0, 5.0)]
    
    clip_paths = await processor.create_clips(
        sample_video_file,
        highlight_ranges,
        temp_dir
    )
    
    # Should return empty list when FFmpeg fails
    assert len(clip_paths) == 0
