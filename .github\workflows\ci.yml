name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Frontend Tests and Build
  frontend-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run type checking
      run: npm run type-check
    
    - name: Build application
      run: npm run build
      env:
        NEXT_PUBLIC_API_URL: http://localhost:8000

  # Backend Tests
  backend-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run tests with coverage
      run: |
        pytest --cov=app --cov-report=xml --cov-report=term-missing tests/
      env:
        REDIS_URL: redis://localhost:6379/0
        AWS_ACCESS_KEY_ID: test
        AWS_SECRET_ACCESS_KEY: test
        S3_ENDPOINT_URL: http://localhost:4566
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage

  # Worker Tests
  worker-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./worker
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
        cache-dependency-path: worker/requirements.txt
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y ffmpeg
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run tests with coverage
      run: |
        pytest --cov=. --cov-report=xml --cov-report=term-missing tests/
      env:
        REDIS_URL: redis://localhost:6379/0
        AWS_ACCESS_KEY_ID: test
        AWS_SECRET_ACCESS_KEY: test
        S3_ENDPOINT_URL: http://localhost:4566
        WHISPER_MODEL: tiny  # Use smaller model for CI
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./worker/coverage.xml
        flags: worker
        name: worker-coverage

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Docker Build and Push
  docker-build:
    runs-on: ubuntu-latest
    needs: [frontend-test, backend-test, worker-test]
    if: github.event_name == 'push'
    
    strategy:
      matrix:
        service: [frontend, backend, worker]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./${{ matrix.service }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # Integration Tests
  integration-test:
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Create .env file
      run: cp .env.example .env
    
    - name: Start services
      run: |
        docker-compose up -d
        sleep 30  # Wait for services to start
    
    - name: Wait for services to be ready
      run: |
        chmod +x scripts/wait-for-services.sh
        ./scripts/wait-for-services.sh
    
    - name: Run integration tests
      run: |
        # Test API health
        curl -f http://localhost:8000/health
        
        # Test frontend
        curl -f http://localhost:3000
        
        # Test API endpoints
        curl -f http://localhost:8000/info
    
    - name: Cleanup
      if: always()
      run: docker-compose down -v

  # Deployment (placeholder for production deployment)
  deploy:
    runs-on: ubuntu-latest
    needs: [integration-test]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Deploy to production
      run: |
        echo "Deployment would happen here"
        echo "This could deploy to AWS ECS, Kubernetes, etc."
        echo "Using the built Docker images:"
        echo "- ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:latest"
        echo "- ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:latest"
        echo "- ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-worker:latest"
