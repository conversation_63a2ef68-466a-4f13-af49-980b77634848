"""
Tests for status endpoints.
"""

import pytest
from unittest.mock import <PERSON>Mock
from fastapi.testclient import Test<PERSON>lient


@pytest.mark.asyncio
async def test_get_job_status_success(async_client, sample_job_data, mock_job_manager, mock_s3_manager):
    """Test successful job status retrieval."""
    job_id = "test-job-123"
    
    # Mock job manager to return job data
    mock_job_manager.get_job.return_value = sample_job_data
    
    response = await async_client.get(f"/status/{job_id}")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["job_id"] == job_id
    assert data["status"] == "pending"
    assert data["progress"] == 0
    assert data["message"] == "Job created"
    assert data["filename"] == "test_video.mp4"


@pytest.mark.asyncio
async def test_get_job_status_not_found(async_client, mock_job_manager):
    """Test job status retrieval for non-existent job."""
    job_id = "non-existent-job"
    
    # Mock job manager to return None
    mock_job_manager.get_job.return_value = None
    
    response = await async_client.get(f"/status/{job_id}")
    
    assert response.status_code == 404
    data = response.json()
    assert "not found" in data["detail"]


@pytest.mark.asyncio
async def test_get_job_status_completed_with_highlights(async_client, mock_job_manager, mock_s3_manager):
    """Test job status retrieval for completed job with highlights."""
    job_id = "test-job-123"
    
    completed_job_data = {
        "job_id": job_id,
        "status": "completed",
        "progress": 100,
        "message": "Processing complete",
        "highlights": ["highlights/test-job-123/clip_01.mp4", "highlights/test-job-123/clip_02.mp4"],
        "created_at": "2023-12-01T00:00:00Z",
        "updated_at": "2023-12-01T00:05:00Z",
        "filename": "test_video.mp4"
    }
    
    # Mock job manager
    mock_job_manager.get_job.return_value = completed_job_data
    
    # Mock S3 manager to generate download URLs
    mock_s3_manager.generate_presigned_download_url.side_effect = [
        "https://test-bucket.s3.amazonaws.com/clip_01.mp4?signature=test1",
        "https://test-bucket.s3.amazonaws.com/clip_02.mp4?signature=test2"
    ]
    
    response = await async_client.get(f"/status/{job_id}")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "completed"
    assert data["progress"] == 100
    assert len(data["highlights"]) == 2
    assert "signature=test1" in data["highlights"][0]
    assert "signature=test2" in data["highlights"][1]


@pytest.mark.asyncio
async def test_get_job_status_failed(async_client, mock_job_manager):
    """Test job status retrieval for failed job."""
    job_id = "test-job-123"
    
    failed_job_data = {
        "job_id": job_id,
        "status": "failed",
        "progress": 50,
        "message": "Processing failed",
        "error_message": "Video processing error",
        "error_details": {"error_type": "ProcessingError"},
        "created_at": "2023-12-01T00:00:00Z",
        "updated_at": "2023-12-01T00:03:00Z",
        "filename": "test_video.mp4"
    }
    
    # Mock job manager
    mock_job_manager.get_job.return_value = failed_job_data
    
    response = await async_client.get(f"/status/{job_id}")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "failed"
    assert data["error_message"] == "Video processing error"
    assert data["error_details"]["error_type"] == "ProcessingError"


@pytest.mark.asyncio
async def test_get_job_highlights_success(async_client, mock_job_manager, mock_s3_manager):
    """Test successful job highlights retrieval."""
    job_id = "test-job-123"
    
    completed_job_data = {
        "job_id": job_id,
        "status": "completed",
        "highlights": ["highlights/test-job-123/clip_01.mp4", "highlights/test-job-123/clip_02.mp4"]
    }
    
    # Mock job manager
    mock_job_manager.get_job.return_value = completed_job_data
    
    # Mock S3 manager
    mock_s3_manager.generate_presigned_download_url.side_effect = [
        "https://test-bucket.s3.amazonaws.com/clip_01.mp4?signature=test1",
        "https://test-bucket.s3.amazonaws.com/clip_02.mp4?signature=test2"
    ]
    
    response = await async_client.get(f"/status/{job_id}/highlights")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["job_id"] == job_id
    assert data["total_count"] == 2
    assert len(data["highlights"]) == 2
    assert data["highlights"][0]["filename"] == "clip_01.mp4"
    assert data["highlights"][1]["filename"] == "clip_02.mp4"


@pytest.mark.asyncio
async def test_get_job_highlights_not_completed(async_client, mock_job_manager):
    """Test job highlights retrieval for non-completed job."""
    job_id = "test-job-123"
    
    pending_job_data = {
        "job_id": job_id,
        "status": "processing"
    }
    
    # Mock job manager
    mock_job_manager.get_job.return_value = pending_job_data
    
    response = await async_client.get(f"/status/{job_id}/highlights")
    
    assert response.status_code == 400
    data = response.json()
    assert "not completed yet" in data["detail"]


@pytest.mark.asyncio
async def test_get_job_highlights_no_highlights(async_client, mock_job_manager):
    """Test job highlights retrieval when no highlights found."""
    job_id = "test-job-123"
    
    completed_job_data = {
        "job_id": job_id,
        "status": "completed",
        "highlights": []
    }
    
    # Mock job manager
    mock_job_manager.get_job.return_value = completed_job_data
    
    response = await async_client.get(f"/status/{job_id}/highlights")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["job_id"] == job_id
    assert len(data["highlights"]) == 0
    assert "No highlights found" in data["message"]


@pytest.mark.asyncio
async def test_delete_job_success(async_client, mock_job_manager, mock_s3_manager, mock_redis_client):
    """Test successful job deletion."""
    job_id = "test-job-123"
    
    job_data = {
        "job_id": job_id,
        "object_key": "uploads/test-job-123/test_video.mp4",
        "highlights": ["highlights/test-job-123/clip_01.mp4"]
    }
    
    # Mock job manager
    mock_job_manager.get_job.return_value = job_data
    mock_job_manager.redis = mock_redis_client
    mock_job_manager.job_prefix = "job:"
    
    # Mock S3 manager
    mock_s3_manager.delete_object.return_value = True
    
    # Mock Redis delete
    mock_redis_client.delete.return_value = 1
    
    response = await async_client.delete(f"/status/{job_id}")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "success"
    assert data["job_id"] == job_id
    assert "deleted successfully" in data["message"]


@pytest.mark.asyncio
async def test_delete_job_not_found(async_client, mock_job_manager):
    """Test job deletion for non-existent job."""
    job_id = "non-existent-job"
    
    # Mock job manager to return None
    mock_job_manager.get_job.return_value = None
    
    response = await async_client.delete(f"/status/{job_id}")
    
    assert response.status_code == 404
    data = response.json()
    assert "not found" in data["detail"]
