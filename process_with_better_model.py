#!/usr/bin/env python3
"""
Process the full video with improved Whisper model for better accuracy
"""

import os
import sys
import asyncio
from datetime import datetime
from dotenv import load_dotenv

# Load environment
load_dotenv('.env')

# Import our improved processor
from improved_video_processor import ImprovedVideoProcessor

def create_output_directory():
    """Create output directory for clips"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"improved_clips_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

async def process_with_better_model():
    """Process the full video with improved Whisper model"""
    print("🎮 Processing Video with Improved Whisper Model")
    print("=" * 70)
    
    # Video file path
    video_path = "backend/samples/24 Kills + MANIAC!! Alucard Best Build Exp Lane!! - Build Top 1 Global Alucard ~ MLBB.mp4"
    
    if not os.path.exists(video_path):
        print("❌ Sample video not found!")
        return False
    
    # Get video info
    file_size = os.path.getsize(video_path)
    print(f"📹 Video: {os.path.basename(video_path)}")
    print(f"📊 Size: {file_size / (1024*1024):.1f} MB")
    
    # Create output directory
    output_dir = create_output_directory()
    print(f"📁 Output directory: {output_dir}")
    
    try:
        # Initialize improved processor
        processor = ImprovedVideoProcessor()
        print("✅ Improved video processor initialized")
        
        # Try to load better model (base > tiny for accuracy)
        model_loaded = await processor.initialize("base")  # Try base model first
        
        if not model_loaded:
            print("❌ Failed to load any Whisper model")
            return False
        
        print(f"\n🎯 Gaming Events We're Looking For:")
        for event, phrases in list(processor.gaming_phrases.items())[:6]:  # Show first 6
            print(f"   {event}: {phrases[0]} (+ {len(phrases)-1} variations)")
        print(f"   ... and {len(processor.gaming_phrases)-6} more events")
        
        print(f"\n🔊 Step 1: Extracting transcript with improved settings...")
        
        # Extract transcript with gaming-optimized settings
        transcript = await processor.extract_transcript_improved(video_path)
        segments = transcript.get('segments', [])
        
        # Show transcript summary
        total_duration = transcript.get('duration', 0)
        print(f"📊 Video duration: {total_duration:.1f} seconds ({total_duration/60:.1f} minutes)")
        
        print(f"\n🎯 Step 2: Detecting gaming events with flexible matching...")
        
        # Detect highlights with flexible matching
        highlight_ranges = await processor.detect_highlights_flexible(transcript)
        
        if highlight_ranges:
            print(f"\n🎉 SUCCESS! Found {len(highlight_ranges)} gaming events!")
            
            print(f"\n🎬 Gaming Events Detected:")
            total_highlight_duration = 0
            for i, (start, end, event) in enumerate(highlight_ranges):
                duration = end - start
                total_highlight_duration += duration
                print(f"   {i+1:2d}. {event:25s}: {start:6.1f}s - {end:6.1f}s ({duration:5.1f}s)")
            
            print(f"\n📊 Summary:")
            print(f"   • Total clips: {len(highlight_ranges)}")
            print(f"   • Total duration: {total_highlight_duration:.1f} seconds")
            print(f"   • Average clip: {total_highlight_duration/len(highlight_ranges):.1f} seconds")
            
            # Categorize events
            events_found = {}
            for _, _, event in highlight_ranges:
                base_event = event.replace('_sequence', '')
                events_found[base_event] = events_found.get(base_event, 0) + 1
            
            print(f"\n🏆 Events Breakdown:")
            for event, count in sorted(events_found.items()):
                print(f"   • {event}: {count} clips")
            
            print(f"\n🎞️  Step 3: Creating video clips...")
            
            # Create clips
            clip_paths = await processor.create_clips_with_names(
                video_path, highlight_ranges, output_dir
            )
            
            print(f"\n✅ Created {len(clip_paths)} clips successfully!")
            
            # Show created files
            print(f"\n📁 Created Files:")
            total_size = 0
            for clip_path in clip_paths:
                if os.path.exists(clip_path):
                    size = os.path.getsize(clip_path)
                    total_size += size
                    print(f"   • {os.path.basename(clip_path)} ({size/(1024*1024):.1f} MB)")
                else:
                    print(f"   ❌ {os.path.basename(clip_path)} (failed)")
            
            print(f"\n📊 Final Results:")
            print(f"   • Input video: {file_size/(1024*1024):.1f} MB")
            print(f"   • Output clips: {total_size/(1024*1024):.1f} MB")
            print(f"   • Compression ratio: {(total_size/file_size)*100:.1f}%")
            print(f"   • Clips directory: {output_dir}")
            
            # Create summary file
            summary_path = os.path.join(output_dir, "gaming_highlights_summary.txt")
            with open(summary_path, 'w') as f:
                f.write("Gaming Highlights Extraction Summary\n")
                f.write("=" * 40 + "\n\n")
                f.write(f"Source Video: {os.path.basename(video_path)}\n")
                f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Whisper Model: Improved (base/tiny with gaming optimization)\n")
                f.write(f"Detection Method: Flexible phrase matching\n")
                f.write(f"Total Clips: {len(highlight_ranges)}\n")
                f.write(f"Total Duration: {total_highlight_duration:.1f} seconds\n\n")
                
                f.write("Detected Gaming Events:\n")
                for i, (start, end, event) in enumerate(highlight_ranges):
                    duration = end - start
                    f.write(f"{i+1:2d}. {event:25s}: {start:6.1f}s - {end:6.1f}s ({duration:5.1f}s)\n")
                
                f.write(f"\nEvents Breakdown:\n")
                for event, count in sorted(events_found.items()):
                    f.write(f"• {event}: {count} clips\n")
                
                f.write(f"\nTranscript Analysis:\n")
                f.write(f"• Total segments: {len(segments)}\n")
                f.write(f"• Video duration: {total_duration:.1f} seconds\n")
                f.write(f"• Detection accuracy: Improved with flexible matching\n")
            
            print(f"✅ Summary saved to: {summary_path}")
            
            return {
                'success': True,
                'clips_created': len(clip_paths),
                'total_duration': total_highlight_duration,
                'events_found': events_found,
                'output_dir': output_dir
            }
        
        else:
            print("⚠️  No gaming events detected even with improved model!")
            print("\n💡 This suggests:")
            print("   • Video might not contain standard gaming announcements")
            print("   • Gaming events use very different phrasing")
            print("   • Audio is heavily distorted or in different language")
            print("   • Background music/noise is too loud")
            
            return {
                'success': True,
                'clips_created': 0,
                'total_duration': 0,
                'events_found': {},
                'output_dir': output_dir
            }
    
    except Exception as e:
        print(f"❌ Error processing video: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False}

def main():
    """Run the improved video processing"""
    print("🚀 Video Highlight Extractor - Improved Processing")
    print("Using better Whisper model + flexible phrase matching")
    print("Should detect more gaming events accurately!")
    
    result = asyncio.run(process_with_better_model())
    
    if result['success']:
        print("\n" + "=" * 70)
        print("🎉 IMPROVED PROCESSING COMPLETE!")
        
        if result['clips_created'] > 0:
            print(f"\n🏆 IMPROVED RESULTS:")
            print(f"   • Clips created: {result['clips_created']}")
            print(f"   • Total duration: {result['total_duration']:.1f} seconds")
            print(f"   • Events detected: {len(result['events_found'])}")
            print(f"   • Output folder: {result['output_dir']}")
            
            print(f"\n🎮 Gaming Events Found:")
            for event, count in result['events_found'].items():
                print(f"   • {event}: {count} clips")
            
            print(f"\n📁 Your improved clips are ready!")
            print(f"   Location: {result['output_dir']}")
            print(f"   Each clip shows the exact gaming event detected")
            
            print(f"\n💡 Improvements made:")
            print(f"   ✅ Better Whisper model (base vs tiny)")
            print(f"   ✅ Gaming-optimized transcription settings")
            print(f"   ✅ Flexible phrase matching")
            print(f"   ✅ Multiple phrase variations per event")
            print(f"   ✅ Better handling of audio quality issues")
        else:
            print(f"\n⚠️  Still no events detected")
            print(f"   The video might not contain recognizable gaming announcements")
            print(f"   Check the transcript output to see what was actually said")
    else:
        print("\n❌ Improved processing failed")
    
    return result['success']

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
