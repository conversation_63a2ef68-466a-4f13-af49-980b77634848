"""
Video processing module for highlight extraction.
Handles Whisper transcription, highlight detection, and FFmpeg video cutting.
"""

import asyncio
import logging
import os
import re
from typing import List, Dict, Any, Tuple, Optional

import whisper
import ffmpeg
from core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class VideoProcessor:
    """Main video processing class for highlight extraction."""
    
    def __init__(self):
        self.whisper_model = None
        self.highlight_phrases = settings.HIGHLIGHT_PHRASES
        self.multi_kill_sequences = settings.MULTI_KILL_SEQUENCES
        self.merge_gap_threshold = settings.MERGE_GAP_THRESHOLD
    
    async def initialize(self):
        """Initialize the video processor (load Whisper model)."""
        logger.info(f"Loading Whisper model: {settings.WHISPER_MODEL}")
        
        # Load Whisper model in a thread to avoid blocking
        loop = asyncio.get_event_loop()
        self.whisper_model = await loop.run_in_executor(
            None, 
            whisper.load_model, 
            settings.WHISPER_MODEL
        )
        
        logger.info("Whisper model loaded successfully")
    
    async def extract_transcript(self, video_path: str) -> Dict[str, Any]:
        """
        Extract transcript from video using Whisper with word-level timestamps.
        
        Args:
            video_path: Path to the input video file
            
        Returns:
            Whisper transcript result with word-level timestamps
        """
        logger.info(f"Extracting transcript from {video_path}")
        
        if not self.whisper_model:
            raise RuntimeError("Whisper model not initialized")
        
        # Run Whisper transcription in a thread
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            lambda: self.whisper_model.transcribe(
                video_path,
                word_timestamps=True,
                verbose=False
            )
        )
        
        logger.info(f"Transcript extracted: {len(result.get('segments', []))} segments")
        return result
    
    async def detect_highlights(self, transcript: Dict[str, Any]) -> List[Tuple[float, float]]:
        """
        Enhanced highlight detection with multi-kill sequence support.

        Args:
            transcript: Whisper transcript result

        Returns:
            List of (start_time, end_time) tuples for highlight clips
        """
        logger.info("Detecting highlights in transcript with enhanced multi-kill support")

        segments = transcript.get("segments", [])
        if not segments:
            logger.warning("No segments found in transcript")
            return []

        # First, find all individual phrase matches with timestamps
        all_matches = []

        for segment in segments:
            words = segment.get("words", [])
            if not words:
                continue

            # Check each phrase in the segment
            for phrase, config in self.highlight_phrases.items():
                start_offset, end_offset, is_sequence = config

                # Find phrase matches in the segment
                matches = self._find_phrase_matches(words, phrase)

                for match_time in matches:
                    all_matches.append({
                        'phrase': phrase,
                        'time': match_time,
                        'start_offset': start_offset,
                        'end_offset': end_offset,
                        'is_sequence': is_sequence
                    })
                    logger.info(f"Found '{phrase}' at {match_time:.2f}s")

        # Sort matches by time
        all_matches.sort(key=lambda x: x['time'])

        # Process matches to create highlight ranges
        highlight_ranges = []
        processed_matches = set()

        for i, match in enumerate(all_matches):
            if i in processed_matches:
                continue

            phrase = match['phrase']

            # Check if this is a sequence event
            if match['is_sequence'] and phrase in self.multi_kill_sequences:
                sequence_range = self._process_multi_kill_sequence(
                    phrase, match, all_matches, i, processed_matches
                )
                if sequence_range:
                    highlight_ranges.append(sequence_range)
            else:
                # Single event highlight
                clip_start = max(0, match['time'] + match['start_offset'])
                clip_end = match['time'] + match['end_offset']
                highlight_ranges.append((clip_start, clip_end))
                processed_matches.add(i)
                logger.info(f"Created single highlight for '{phrase}': {clip_start:.2f}s-{clip_end:.2f}s")

        # Merge overlapping ranges
        merged_ranges = self._merge_overlapping_ranges(highlight_ranges)

        logger.info(f"Detected {len(merged_ranges)} highlight clips after merging")
        return merged_ranges
    
    def _find_phrase_matches(self, words: List[Dict], phrase: str) -> List[float]:
        """
        Find occurrences of a phrase in the word list.
        
        Args:
            words: List of word dictionaries with timestamps
            phrase: Phrase to search for
            
        Returns:
            List of timestamps where the phrase was found
        """
        matches = []
        phrase_words = phrase.lower().split()
        
        if len(phrase_words) == 1:
            # Single word search
            target_word = phrase_words[0]
            for word_info in words:
                word_text = word_info.get("word", "").lower().strip()
                # Remove punctuation for matching
                word_text = re.sub(r'[^\w\s]', '', word_text)
                
                if target_word in word_text or word_text in target_word:
                    matches.append(word_info.get("start", 0))
        else:
            # Multi-word phrase search
            for i in range(len(words) - len(phrase_words) + 1):
                word_sequence = []
                for j in range(len(phrase_words)):
                    if i + j < len(words):
                        word_text = words[i + j].get("word", "").lower().strip()
                        word_text = re.sub(r'[^\w\s]', '', word_text)
                        word_sequence.append(word_text)
                
                # Check if the sequence matches the phrase
                if len(word_sequence) == len(phrase_words):
                    sequence_text = " ".join(word_sequence)
                    if phrase.lower() in sequence_text:
                        matches.append(words[i].get("start", 0))
        
        return matches

    def _process_multi_kill_sequence(
        self,
        target_phrase: str,
        target_match: Dict,
        all_matches: List[Dict],
        target_index: int,
        processed_matches: set
    ) -> Tuple[float, float]:
        """
        Process multi-kill sequences to capture the full sequence of events.

        Enhanced to handle multiple possible sequences for double_kill:
        - double_kill: first_blood -> double_kill OR you_have_slain_an_enemy -> double_kill
        - triple_kill: you_have_slain_an_enemy -> double_kill -> triple_kill
        - mega_kill: you_have_slain_an_enemy -> double_kill -> triple_kill -> mega_kill
        - maniac: you_have_slain_an_enemy -> double_kill -> triple_kill -> mega_kill -> maniac
        - savage: you_have_slain_an_enemy -> double_kill -> triple_kill -> mega_kill -> maniac -> savage

        Args:
            target_phrase: The target phrase (e.g., "maniac")
            target_match: The match data for the target phrase
            all_matches: All phrase matches sorted by time
            target_index: Index of target match in all_matches
            processed_matches: Set of already processed match indices

        Returns:
            Tuple of (start_time, end_time) for the sequence clip
        """
        sequence_definition = self.multi_kill_sequences.get(target_phrase, [])
        if not sequence_definition:
            return None

        target_time = target_match['time']
        sequence_window = 30.0  # Look for sequence events within 30 seconds before target

        # Handle special case for double_kill which has multiple possible sequences
        if target_phrase == "double_kill" and isinstance(sequence_definition[0], list):
            # Try each possible sequence for double_kill
            best_sequence = None
            best_matches = []

            for possible_sequence in sequence_definition:
                sequence_matches = self._find_sequence_matches(
                    possible_sequence, target_match, all_matches, target_index, sequence_window
                )

                # Choose the sequence with more matches or the most recent one
                if len(sequence_matches) > len(best_matches):
                    best_sequence = possible_sequence
                    best_matches = sequence_matches

            if best_matches:
                return self._create_sequence_clip(best_matches, target_phrase, processed_matches)
        else:
            # Standard sequence processing
            sequence_matches = self._find_sequence_matches(
                sequence_definition, target_match, all_matches, target_index, sequence_window
            )

            if sequence_matches:
                return self._create_sequence_clip(sequence_matches, target_phrase, processed_matches)

        # Fallback to single event if no sequence found
        clip_start = max(0, target_time + target_match['start_offset'])
        clip_end = target_time + target_match['end_offset']
        processed_matches.add(target_index)
        return (clip_start, clip_end)

    def _find_sequence_matches(
        self,
        sequence_events: List[str],
        target_match: Dict,
        all_matches: List[Dict],
        target_index: int,
        sequence_window: float
    ) -> List[Tuple[int, Dict]]:
        """Find matches for a specific sequence of events."""
        target_time = target_match['time']
        sequence_matches = []

        # Look backwards from target event to find sequence events
        for i in range(target_index - 1, -1, -1):
            match = all_matches[i]
            time_diff = target_time - match['time']

            # Stop looking if we're too far back in time
            if time_diff > sequence_window:
                break

            # Check if this match is part of our sequence
            if match['phrase'] in sequence_events:
                sequence_matches.append((i, match))

        # Add the target match itself
        sequence_matches.append((target_index, target_match))

        # Sort sequence matches by time
        sequence_matches.sort(key=lambda x: x[1]['time'])

        return sequence_matches

    def _create_sequence_clip(
        self,
        sequence_matches: List[Tuple[int, Dict]],
        target_phrase: str,
        processed_matches: set
    ) -> Tuple[float, float]:
        """Create a clip from sequence matches."""
        if len(sequence_matches) < 2:
            # Not enough events for a sequence
            match_index, match_data = sequence_matches[0]
            clip_start = max(0, match_data['time'] + match_data['start_offset'])
            clip_end = match_data['time'] + match_data['end_offset']
            processed_matches.add(match_index)
            return (clip_start, clip_end)

        # Calculate sequence clip timing
        first_match = sequence_matches[0][1]
        last_match = sequence_matches[-1][1]

        # Start from before the first event, end after the last event
        clip_start = max(0, first_match['time'] + first_match['start_offset'])
        clip_end = last_match['time'] + last_match['end_offset']

        # Mark all sequence matches as processed
        for match_index, match_data in sequence_matches:
            processed_matches.add(match_index)

        sequence_phrase_names = [match[1]['phrase'] for match in sequence_matches]
        logger.info(f"Created sequence highlight for {target_phrase}: {' -> '.join(sequence_phrase_names)} ({clip_start:.2f}s-{clip_end:.2f}s)")

        return (clip_start, clip_end)
    
    def _merge_overlapping_ranges(self, ranges: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """
        Merge overlapping or close highlight ranges.
        
        Args:
            ranges: List of (start, end) time ranges
            
        Returns:
            List of merged ranges
        """
        if not ranges:
            return []
        
        # Sort ranges by start time
        sorted_ranges = sorted(ranges, key=lambda x: x[0])
        merged = [sorted_ranges[0]]
        
        for current_start, current_end in sorted_ranges[1:]:
            last_start, last_end = merged[-1]
            
            # Check if ranges overlap or are close enough to merge
            if current_start <= last_end + self.merge_gap_threshold:
                # Merge ranges
                merged[-1] = (last_start, max(last_end, current_end))
            else:
                # Add as separate range
                merged.append((current_start, current_end))
        
        return merged
    
    async def create_clips(
        self, 
        input_video_path: str, 
        highlight_ranges: List[Tuple[float, float]], 
        output_dir: str
    ) -> List[str]:
        """
        Create highlight clips from the input video using FFmpeg.
        
        Args:
            input_video_path: Path to the input video
            highlight_ranges: List of (start, end) time ranges
            output_dir: Directory to save clips
            
        Returns:
            List of paths to created clip files
        """
        logger.info(f"Creating {len(highlight_ranges)} clips from {input_video_path}")
        
        clip_paths = []
        
        for i, (start_time, end_time) in enumerate(highlight_ranges):
            clip_filename = f"clip_{i+1:02d}.mp4"
            clip_path = os.path.join(output_dir, clip_filename)
            
            # Calculate duration
            duration = end_time - start_time
            
            try:
                # Use FFmpeg to extract the clip
                await self._extract_clip_ffmpeg(
                    input_video_path,
                    clip_path,
                    start_time,
                    duration
                )
                
                clip_paths.append(clip_path)
                logger.info(f"Created clip {i+1}: {start_time:.2f}s-{end_time:.2f}s -> {clip_path}")
                
            except Exception as e:
                logger.error(f"Failed to create clip {i+1}: {e}")
                continue
        
        logger.info(f"Successfully created {len(clip_paths)} clips")
        return clip_paths
    
    async def _extract_clip_ffmpeg(
        self, 
        input_path: str, 
        output_path: str, 
        start_time: float, 
        duration: float
    ):
        """
        Extract a video clip using FFmpeg.
        
        Args:
            input_path: Input video file path
            output_path: Output clip file path
            start_time: Start time in seconds
            duration: Duration in seconds
        """
        # Build FFmpeg command for lossless cutting
        stream = ffmpeg.input(input_path, ss=start_time, t=duration)
        stream = ffmpeg.output(
            stream,
            output_path,
            vcodec=settings.VIDEO_CODEC,  # 'copy' for lossless
            acodec=settings.AUDIO_CODEC,  # 'copy' for lossless
            avoid_negative_ts='make_zero',
            threads=settings.FFMPEG_THREADS
        )
        
        # Run FFmpeg in a thread to avoid blocking
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            None,
            lambda: ffmpeg.run(stream, overwrite_output=True, quiet=True)
        )
    
    async def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        Get video information using FFmpeg probe.
        
        Args:
            video_path: Path to video file
            
        Returns:
            Dictionary with video information
        """
        try:
            loop = asyncio.get_event_loop()
            probe = await loop.run_in_executor(
                None,
                lambda: ffmpeg.probe(video_path)
            )
            
            video_stream = next(
                (stream for stream in probe['streams'] if stream['codec_type'] == 'video'),
                None
            )
            
            if video_stream:
                return {
                    'duration': float(probe['format']['duration']),
                    'width': int(video_stream['width']),
                    'height': int(video_stream['height']),
                    'fps': eval(video_stream['r_frame_rate']),
                    'codec': video_stream['codec_name'],
                    'size': int(probe['format']['size'])
                }
            else:
                raise ValueError("No video stream found")
                
        except Exception as e:
            logger.error(f"Failed to get video info for {video_path}: {e}")
            raise
