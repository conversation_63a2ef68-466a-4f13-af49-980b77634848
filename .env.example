# Environment Configuration

# Redis Configuration
REDIS_URL=redis://redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# S3 Configuration
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_REGION=us-east-1
S3_ENDPOINT_URL=http://localstack:4566
INPUT_BUCKET=video-uploads
OUTPUT_BUCKET=video-highlights

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
FRONTEND_URL=http://localhost:3000

# Worker Configuration
WORKER_CONCURRENCY=2
WHISPER_MODEL=medium
TEMP_DIR=/tmp/video_processing

# Highlight Detection Settings
HIGHLIGHT_PHRASES={"double kill": [-3, 2], "triple kill": [-3, 2], "maniac": [-4, 2], "savage": [-5, 3], "shutdown": [-2, 2]}
MERGE_GAP_THRESHOLD=1.0

# Development Settings
DEBUG=true
LOG_LEVEL=INFO

# Production Settings (uncomment for production)
# AWS_ACCESS_KEY_ID=your_actual_key
# AWS_SECRET_ACCESS_KEY=your_actual_secret
# S3_ENDPOINT_URL=https://s3.amazonaws.com
# REDIS_URL=redis://your-redis-cluster:6379/0
