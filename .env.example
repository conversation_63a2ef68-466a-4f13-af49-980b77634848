# Environment Configuration

# Redis Configuration
REDIS_URL=redis://redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# Backblaze B2 S3-Compatible Configuration
BACKBLAZE_ACCESS_KEY_ID=005a665ff0e7c360000000003
BACKBLAZE_SECRET_ACCESS_KEY=K005w4ikLOftDCqa3/SuRqQcTdzV6Uk
BACKBLAZE_REGION=us-east-005
BACKBLAZE_ENDPOINT_URL=https://s3.us-east-005.backblazeb2.com
INPUT_BUCKET=MLhighlights
OUTPUT_BUCKET=MLhighlights

# Development S3 Configuration (LocalStack - for local development only)
# Uncomment these and comment above for local development
# BACKBLAZE_ACCESS_KEY_ID=test
# BACKBLAZE_SECRET_ACCESS_KEY=test
# BACKBLAZE_REGION=us-east-1
# BACKBLAZE_ENDPOINT_URL=http://localstack:4566
# INPUT_BUCKET=video-uploads
# OUTPUT_BUCKET=video-highlights

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
FRONTEND_URL=http://localhost:3000

# Worker Configuration
WORKER_CONCURRENCY=2
WHISPER_MODEL=medium
TEMP_DIR=/tmp/video_processing

# Enhanced Highlight Detection Settings
# Format: "phrase": [seconds_before, seconds_after, sequence_capture]
# sequence_capture: true for multi-kill events that should capture the full sequence
HIGHLIGHT_PHRASES={"double_kill": [-5, 3, true], "first_blood": [-3, 2, false], "godlike": [-8, 5, true], "you_have_slain_an_enemy": [-2, 1, false], "killing_spree": [-5, 3, true], "legendary": [-8, 5, true], "maniac": [-12, 6, true], "mega_kill": [-8, 5, true], "monster_kill": [-4, 3, true], "triple_kill": [-8, 5, true], "unstoppable": [-8, 5, true], "savage": [-15, 7, true]}

# Multi-kill sequence definitions (what events to capture for sequence events)
# double_kill can have two possible sequences: first_blood OR you_have_slain_an_enemy
MULTI_KILL_SEQUENCES={"double_kill": [["first_blood", "double_kill"], ["you_have_slain_an_enemy", "double_kill"]], "triple_kill": ["you_have_slain_an_enemy", "double_kill", "triple_kill"], "mega_kill": ["you_have_slain_an_enemy", "double_kill", "triple_kill", "mega_kill"], "maniac": ["you_have_slain_an_enemy", "double_kill", "triple_kill", "mega_kill", "maniac"], "savage": ["you_have_slain_an_enemy", "double_kill", "triple_kill", "mega_kill", "maniac", "savage"], "godlike": ["killing_spree", "legendary", "godlike"], "unstoppable": ["killing_spree", "legendary", "godlike", "unstoppable"]}

MERGE_GAP_THRESHOLD=2.0

# Development Settings
DEBUG=true
LOG_LEVEL=INFO

# Production Settings (uncomment for production)
# AWS_ACCESS_KEY_ID=your_actual_key
# AWS_SECRET_ACCESS_KEY=your_actual_secret
# S3_ENDPOINT_URL=https://s3.amazonaws.com
# REDIS_URL=redis://your-redis-cluster:6379/0
