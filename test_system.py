#!/usr/bin/env python3
"""
Comprehensive system test for Video Highlight Extractor
Tests Backblaze B2 integration and enhanced highlight detection
"""

import os
import sys
from dotenv import load_dotenv

def test_environment():
    """Test environment configuration"""
    print("🔧 Testing Environment Configuration...")
    
    # Load environment variables
    load_dotenv('.env')
    
    # Check Backblaze configuration
    backblaze_endpoint = os.getenv('BACKBLAZE_ENDPOINT_URL')
    input_bucket = os.getenv('INPUT_BUCKET')
    access_key = os.getenv('BACKBLAZE_ACCESS_KEY_ID')
    
    if backblaze_endpoint and 'backblazeb2.com' in backblaze_endpoint:
        print("✅ Backblaze B2 endpoint configured")
        print(f"   Endpoint: {backblaze_endpoint}")
    else:
        print("❌ Backblaze B2 endpoint not configured")
        return False
    
    if input_bucket == 'MLhighlights':
        print("✅ Bucket configured correctly")
        print(f"   Bucket: {input_bucket}")
    else:
        print("❌ Bucket not configured correctly")
        return False
    
    if access_key and access_key.startswith('005a665ff0e7c360000000003'):
        print("✅ Backblaze access key configured")
    else:
        print("❌ Backblaze access key not configured")
        return False
    
    return True

def test_backend_config():
    """Test backend configuration"""
    print("\n🔧 Testing Backend Configuration...")
    
    try:
        sys.path.append('backend')
        from app.core.config import get_settings
        
        settings = get_settings()
        
        # Test Backblaze configuration
        if settings.BACKBLAZE_ENDPOINT_URL == 'https://s3.us-east-005.backblazeb2.com':
            print("✅ Backend Backblaze endpoint configured")
        else:
            print(f"❌ Backend endpoint: {settings.BACKBLAZE_ENDPOINT_URL}")
            return False
        
        # Test highlight phrases
        if len(settings.HIGHLIGHT_PHRASES) == 12:
            print("✅ All 12 gaming events configured (including savage)")
            print("   Events:", list(settings.HIGHLIGHT_PHRASES.keys()))
        else:
            print(f"❌ Only {len(settings.HIGHLIGHT_PHRASES)} events configured")
            return False

        # Test multi-kill sequences
        if len(settings.MULTI_KILL_SEQUENCES) == 7:
            print("✅ Multi-kill sequences configured")
            print("   Sequences:", list(settings.MULTI_KILL_SEQUENCES.keys()))
        else:
            print(f"❌ Only {len(settings.MULTI_KILL_SEQUENCES)} sequences configured")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Backend configuration error: {e}")
        return False

def test_worker_config():
    """Test worker configuration"""
    print("\n🔧 Testing Worker Configuration...")
    
    try:
        sys.path.append('worker')
        from core.config import get_settings
        
        settings = get_settings()
        
        # Test Backblaze configuration
        if settings.BACKBLAZE_ENDPOINT_URL == 'https://s3.us-east-005.backblazeb2.com':
            print("✅ Worker Backblaze endpoint configured")
        else:
            print(f"❌ Worker endpoint: {settings.BACKBLAZE_ENDPOINT_URL}")
            return False
        
        # Test enhanced highlight detection
        if len(settings.HIGHLIGHT_PHRASES) == 12:
            print("✅ Worker has all 12 gaming events (including savage)")
        else:
            print(f"❌ Worker only has {len(settings.HIGHLIGHT_PHRASES)} events")
            return False

        # Test sequence configuration
        if len(settings.MULTI_KILL_SEQUENCES) == 7:
            print("✅ Worker has multi-kill sequences")
        else:
            print(f"❌ Worker only has {len(settings.MULTI_KILL_SEQUENCES)} sequences")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Worker configuration error: {e}")
        return False

def test_s3_connection():
    """Test Backblaze B2 connection"""
    print("\n🔧 Testing Backblaze B2 Connection...")
    
    try:
        sys.path.append('backend')
        from app.core.s3_client import get_s3_client
        
        client = get_s3_client()
        buckets = client.list_buckets()
        
        print(f"✅ Successfully connected to Backblaze B2")
        print(f"✅ Found {len(buckets['Buckets'])} buckets")
        
        # Check if our bucket exists
        bucket_names = [bucket['Name'] for bucket in buckets['Buckets']]
        if 'MLhighlights' in bucket_names:
            print("✅ MLhighlights bucket found")
        else:
            print("⚠️  MLhighlights bucket not found")
            print("   Available buckets:", bucket_names)
        
        return True
        
    except Exception as e:
        print(f"❌ Backblaze B2 connection error: {e}")
        return False

def test_highlight_detection():
    """Test enhanced highlight detection logic"""
    print("\n🔧 Testing Enhanced Highlight Detection...")
    
    try:
        sys.path.append('worker')
        from processors.video_processor import VideoProcessor
        
        processor = VideoProcessor()
        
        # Test configuration
        if len(processor.highlight_phrases) == 12:
            print("✅ Video processor has all 12 events (including savage)")
        else:
            print(f"❌ Video processor only has {len(processor.highlight_phrases)} events")
            return False

        if len(processor.multi_kill_sequences) == 7:
            print("✅ Video processor has multi-kill sequences")
        else:
            print(f"❌ Video processor only has {len(processor.multi_kill_sequences)} sequences")
            return False
        
        # Test specific events
        test_events = ['double_kill', 'maniac', 'triple_kill', 'godlike']
        for event in test_events:
            if event in processor.highlight_phrases:
                config = processor.highlight_phrases[event]
                print(f"✅ {event}: {config[0]}s to +{config[1]}s, sequence={config[2]}")
            else:
                print(f"❌ {event} not configured")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Highlight detection error: {e}")
        return False

def test_frontend_build():
    """Test frontend build"""
    print("\n🔧 Testing Frontend...")
    
    try:
        # Check if node_modules exists
        if os.path.exists('frontend/node_modules'):
            print("✅ Frontend dependencies installed")
        else:
            print("⚠️  Frontend dependencies not installed")
            return False
        
        # Check if package.json has correct dependencies
        import json
        with open('frontend/package.json', 'r') as f:
            package_data = json.load(f)
        
        required_deps = ['next', 'react', 'react-dropzone', 'axios', 'tailwindcss']
        for dep in required_deps:
            if dep in package_data.get('dependencies', {}) or dep in package_data.get('devDependencies', {}):
                print(f"✅ {dep} dependency found")
            else:
                print(f"❌ {dep} dependency missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Frontend test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Video Highlight Extractor - System Test")
    print("=" * 50)
    
    tests = [
        test_environment,
        test_backend_config,
        test_worker_config,
        test_s3_connection,
        test_highlight_detection,
        test_frontend_build,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print("❌ Test failed!")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for use.")
        print("\n🚀 Quick Start:")
        print("   Production: make up-prod")
        print("   Development: make up-dev")
        print("   Frontend: http://localhost:3000")
        return True
    else:
        print("❌ Some tests failed. Please check the configuration.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
