# Backblaze B2 Integration & Enhanced Highlight Detection

This document outlines the integration of Backblaze B2 storage and enhanced gaming highlight detection with multi-kill sequences.

## 🔧 Backblaze B2 Integration

### Configuration Changes

**Environment Variables (Updated):**
```bash
# Backblaze B2 S3-Compatible Configuration
BACKBLAZE_ACCESS_KEY_ID=005a665ff0e7c360000000003
BACKBLAZE_SECRET_ACCESS_KEY=K005w4ikLOftDCqa3/SuRqQcTdzV6Uk
BACKBLAZE_REGION=us-east-005
BACKBLAZE_ENDPOINT_URL=https://s3.us-east-005.backblazeb2.com
INPUT_BUCKET=MLhighlights
OUTPUT_BUCKET=MLhighlights
```

**Key Benefits:**
- **Cost-effective**: Backblaze B2 is significantly cheaper than AWS S3
- **S3-compatible**: Uses standard S3 API, no code changes needed
- **Reliable**: Enterprise-grade cloud storage
- **Global**: Fast access from anywhere

### Deployment Modes

**Development Mode (LocalStack):**
```bash
make up-dev
# Uses LocalStack for S3 simulation
```

**Production Mode (Backblaze B2):**
```bash
make up-prod
# Uses your Backblaze B2 credentials
```

## 🎮 Enhanced Highlight Detection

### Supported Gaming Events (11 Total)

| Event | Timing | Sequence Capture |
|-------|--------|------------------|
| `double_kill` | -5s to +3s | Single event |
| `first_blood` | -3s to +2s | Single event |
| `triple_kill` | -6s to +4s | **Multi-kill sequence** |
| `mega_kill` | -6s to +4s | **Multi-kill sequence** |
| `monster_kill` | -4s to +3s | **Multi-kill sequence** |
| `maniac` | -10s to +5s | **Multi-kill sequence** |
| `godlike` | -8s to +5s | **Multi-kill sequence** |
| `legendary` | -8s to +5s | **Multi-kill sequence** |
| `unstoppable` | -8s to +5s | **Multi-kill sequence** |
| `killing_spree` | -5s to +3s | **Multi-kill sequence** |
| `you_have_slain_an_enemy` | -2s to +1s | Single event |

### Multi-Kill Sequence Capture

**Enhanced Feature**: For multi-kill events, the system captures the entire sequence leading up to the event:

**Triple Kill Sequence:**
```
you_have_slain_an_enemy → double_kill → triple_kill
```
*Captures the full progression from first kill to triple kill*

**Maniac Sequence:**
```
you_have_slain_an_enemy → double_kill → triple_kill → mega_kill → maniac
```
*Captures the complete rampage from first kill to maniac*

**Godlike Sequence:**
```
killing_spree → legendary → godlike
```
*Captures the streak progression*

**Unstoppable Sequence:**
```
killing_spree → legendary → godlike → unstoppable
```
*Captures the ultimate streak progression*

### Technical Implementation

**Algorithm:**
1. **Phrase Detection**: Whisper AI detects all gaming phrases with word-level timestamps
2. **Sequence Analysis**: For multi-kill events, looks backward up to 30 seconds to find sequence events
3. **Intelligent Clipping**: Creates clips that capture the full sequence, not just the final event
4. **Merge Optimization**: Combines overlapping clips with 2-second gap threshold

**Example Output:**
- Single "double_kill" → 8-second clip (-5s to +3s)
- "maniac" sequence → 15-second clip capturing entire rampage
- "godlike" sequence → 13-second clip showing streak buildup

## 🚀 Usage Instructions

### Quick Start with Backblaze B2

1. **Set up environment:**
```bash
cp .env.example .env
# Your Backblaze credentials are already configured in .env
```

2. **Start production system:**
```bash
make up-prod
```

3. **Upload gaming video:**
- Visit http://localhost:3000
- Drag and drop your gaming video
- Watch real-time processing

4. **Download enhanced highlights:**
- Get individual clips for each detected event
- Multi-kill sequences show complete rampage footage

### Development with LocalStack

```bash
# For development/testing without using Backblaze storage
make up-dev
```

### Configuration Customization

**Adjust highlight timing:**
```bash
# In .env file, modify HIGHLIGHT_PHRASES
# Format: "event": [seconds_before, seconds_after, is_sequence]
HIGHLIGHT_PHRASES={"maniac": [-12, 6, true]}  # Longer maniac clips
```

**Modify sequence definitions:**
```bash
# In .env file, modify MULTI_KILL_SEQUENCES
MULTI_KILL_SEQUENCES={"maniac": ["first_blood", "double_kill", "triple_kill", "mega_kill", "maniac"]}
```

## 📊 Performance Improvements

### Enhanced Detection Accuracy
- **Word-level timestamps**: Precise event timing
- **Sequence awareness**: Captures context, not just individual events
- **Smart merging**: Reduces duplicate clips

### Storage Optimization
- **Backblaze B2**: 80% cost savings vs AWS S3
- **Lossless cutting**: FFmpeg copy codecs for fast processing
- **Pre-signed URLs**: Direct upload/download without server load

### Processing Intelligence
- **Adaptive timing**: Different clip lengths for different events
- **Sequence detection**: Understands gaming event relationships
- **Context preservation**: Maintains narrative flow in clips

## 🔍 Monitoring & Debugging

### Check Backblaze Connection
```bash
# View API logs
make logs-api

# Check worker processing
make logs-worker

# Verify storage operations
curl http://localhost:8000/health/detailed
```

### Development Tools
```bash
# Redis Commander (development mode)
http://localhost:8081

# API Documentation
http://localhost:8000/docs

# System information
curl http://localhost:8000/info
```

## 🎯 Gaming Event Examples

### MOBA Games (League of Legends, Dota 2)
- **First Blood**: Captures the first kill of the match
- **Double/Triple/Mega Kill**: Multi-kill sequences
- **Maniac**: Complete rampage footage
- **Godlike/Legendary**: Streak achievements

### FPS Games (CS:GO, Valorant)
- **Monster Kill**: Multi-frag rounds
- **Killing Spree**: Hot streaks
- **Unstoppable**: Domination sequences

### Battle Royale (PUBG, Fortnite)
- **Enemy Slain**: Individual eliminations
- **Multi-kills**: Squad wipes and clutches

## 🔧 Troubleshooting

### Backblaze B2 Issues
```bash
# Test connection
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://s3.us-east-005.backblazeb2.com/MLhighlights

# Check bucket permissions
# Ensure bucket allows public read access for highlight downloads
```

### Highlight Detection Issues
```bash
# Check worker logs for phrase detection
make logs-worker

# Verify Whisper model loading
docker-compose exec worker python -c "import whisper; print('Whisper OK')"

# Test with known gaming audio
# Upload a video with clear "double kill" audio
```

### Performance Optimization
```bash
# Use smaller Whisper model for faster processing
WHISPER_MODEL=small  # or 'tiny' for development

# Increase worker concurrency
WORKER_CONCURRENCY=4  # More parallel processing
```

## 📈 Future Enhancements

### Planned Features
- **Custom phrase training**: User-defined gaming events
- **Game-specific detection**: Optimized for different games
- **Real-time streaming**: Live highlight detection
- **Advanced analytics**: Gaming performance metrics

### Integration Opportunities
- **Twitch integration**: Auto-highlight stream moments
- **Discord bots**: Share highlights automatically
- **Tournament tools**: Professional esports highlight reels

---

**Your system is now configured with Backblaze B2 storage and enhanced gaming highlight detection with multi-kill sequence capture!** 🎮✨
