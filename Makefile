# Video Highlight Extractor - Development Makefile

.PHONY: help setup build up down logs test clean lint format security

# Default target
help: ## Show this help message
	@echo "Video Highlight Extractor - Development Commands"
	@echo "================================================"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Setup and Installation
setup: ## Setup development environment
	@echo "Setting up development environment..."
	@chmod +x scripts/*.sh
	@./scripts/setup-dev.sh

install-frontend: ## Install frontend dependencies
	@echo "Installing frontend dependencies..."
	@cd frontend && npm ci

install-backend: ## Install backend dependencies
	@echo "Installing backend dependencies..."
	@cd backend && pip install -r requirements.txt

install-worker: ## Install worker dependencies
	@echo "Installing worker dependencies..."
	@cd worker && pip install -r requirements.txt

install: install-frontend install-backend install-worker ## Install all dependencies

# Docker Operations
build: ## Build all Docker images
	@echo "Building Docker images..."
	@docker-compose build

up: ## Start all services
	@echo "Starting all services..."
	@docker-compose up -d

up-dev: ## Start all services with development profile
	@echo "Starting all services (development mode)..."
	@docker-compose --profile dev up -d

down: ## Stop all services
	@echo "Stopping all services..."
	@docker-compose down

down-clean: ## Stop all services and remove volumes
	@echo "Stopping all services and cleaning up..."
	@docker-compose down -v --remove-orphans

restart: down up ## Restart all services

logs: ## Show logs from all services
	@docker-compose logs -f

logs-api: ## Show API logs
	@docker-compose logs -f api

logs-worker: ## Show worker logs
	@docker-compose logs -f worker

logs-frontend: ## Show frontend logs
	@docker-compose logs -f frontend

# Development
dev-frontend: ## Start frontend in development mode
	@echo "Starting frontend development server..."
	@cd frontend && npm run dev

dev-api: ## Start API in development mode
	@echo "Starting API development server..."
	@cd backend && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

dev-worker: ## Start worker in development mode
	@echo "Starting worker in development mode..."
	@cd worker && python main.py

# Testing
test: ## Run all tests
	@echo "Running all tests..."
	@make test-frontend
	@make test-backend
	@make test-worker

test-frontend: ## Run frontend tests
	@echo "Running frontend tests..."
	@cd frontend && npm test

test-backend: ## Run backend tests
	@echo "Running backend tests..."
	@cd backend && pytest --cov=app tests/

test-worker: ## Run worker tests
	@echo "Running worker tests..."
	@cd worker && pytest --cov=. tests/

test-integration: ## Run integration tests
	@echo "Running integration tests..."
	@docker-compose up -d
	@sleep 30
	@curl -f http://localhost:8000/health
	@curl -f http://localhost:3000
	@docker-compose down

# Code Quality
lint: ## Run linting on all code
	@echo "Running linting..."
	@make lint-frontend
	@make lint-backend
	@make lint-worker

lint-frontend: ## Run frontend linting
	@echo "Linting frontend..."
	@cd frontend && npm run lint

lint-backend: ## Run backend linting
	@echo "Linting backend..."
	@cd backend && flake8 app/ tests/

lint-worker: ## Run worker linting
	@echo "Linting worker..."
	@cd worker && flake8 . --exclude=tests/

format: ## Format all code
	@echo "Formatting code..."
	@make format-frontend
	@make format-backend
	@make format-worker

format-frontend: ## Format frontend code
	@echo "Formatting frontend..."
	@cd frontend && npm run format

format-backend: ## Format backend code
	@echo "Formatting backend..."
	@cd backend && black app/ tests/

format-worker: ## Format worker code
	@echo "Formatting worker..."
	@cd worker && black . --exclude=tests/

# Security
security: ## Run security scans
	@echo "Running security scans..."
	@make security-frontend
	@make security-backend
	@make security-worker

security-frontend: ## Run frontend security scan
	@echo "Scanning frontend for vulnerabilities..."
	@cd frontend && npm audit

security-backend: ## Run backend security scan
	@echo "Scanning backend for vulnerabilities..."
	@cd backend && safety check

security-worker: ## Run worker security scan
	@echo "Scanning worker for vulnerabilities..."
	@cd worker && safety check

# Database and Storage
redis-cli: ## Connect to Redis CLI
	@docker-compose exec redis redis-cli

redis-flush: ## Flush Redis database
	@docker-compose exec redis redis-cli FLUSHALL

s3-ls: ## List S3 buckets (LocalStack)
	@aws --endpoint-url=http://localhost:4566 s3 ls

s3-create-buckets: ## Create S3 buckets (LocalStack)
	@aws --endpoint-url=http://localhost:4566 s3 mb s3://video-uploads
	@aws --endpoint-url=http://localhost:4566 s3 mb s3://video-highlights

# Monitoring and Debugging
ps: ## Show running containers
	@docker-compose ps

top: ## Show container resource usage
	@docker stats

shell-api: ## Open shell in API container
	@docker-compose exec api bash

shell-worker: ## Open shell in worker container
	@docker-compose exec worker bash

shell-frontend: ## Open shell in frontend container
	@docker-compose exec frontend sh

# Cleanup
clean: ## Clean up Docker resources
	@echo "Cleaning up Docker resources..."
	@docker system prune -f
	@docker volume prune -f

clean-all: ## Clean up everything (including images)
	@echo "Cleaning up all Docker resources..."
	@docker system prune -af
	@docker volume prune -f

clean-node: ## Clean node_modules
	@echo "Cleaning node_modules..."
	@rm -rf frontend/node_modules

clean-python: ## Clean Python cache
	@echo "Cleaning Python cache..."
	@find . -type d -name __pycache__ -delete
	@find . -name "*.pyc" -delete

# Production
prod-build: ## Build production images
	@echo "Building production images..."
	@docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

prod-up: ## Start production services
	@echo "Starting production services..."
	@docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

prod-down: ## Stop production services
	@echo "Stopping production services..."
	@docker-compose -f docker-compose.yml -f docker-compose.prod.yml down

# Backup and Restore
backup: ## Backup data
	@echo "Creating backup..."
	@mkdir -p backups
	@docker-compose exec redis redis-cli --rdb /data/dump.rdb
	@docker cp $$(docker-compose ps -q redis):/data/dump.rdb backups/redis-$$(date +%Y%m%d-%H%M%S).rdb

restore: ## Restore data (requires BACKUP_FILE variable)
	@echo "Restoring from backup..."
	@if [ -z "$(BACKUP_FILE)" ]; then echo "Please specify BACKUP_FILE=path/to/backup.rdb"; exit 1; fi
	@docker cp $(BACKUP_FILE) $$(docker-compose ps -q redis):/data/dump.rdb
	@docker-compose restart redis
