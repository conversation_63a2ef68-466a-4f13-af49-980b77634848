"""
Redis client configuration and utilities.
"""

import json
import logging
from typing import Any, Dict, Optional, List
from functools import lru_cache

import redis.asyncio as redis
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


@lru_cache()
def get_redis_client() -> redis.Redis:
    """Get Redis client instance."""
    return redis.from_url(
        settings.REDIS_URL,
        encoding="utf-8",
        decode_responses=True,
        socket_connect_timeout=5,
        socket_timeout=5,
        retry_on_timeout=True,
    )


class RedisJobManager:
    """Redis-based job management for video processing."""
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        self.redis = redis_client or get_redis_client()
        self.job_prefix = "job:"
        self.queue_name = settings.WORKER_QUEUE_NAME
        self.progress_channel = "job_progress"
    
    async def create_job(self, job_id: str, job_data: Dict[str, Any]) -> None:
        """Create a new job in Redis."""
        job_key = f"{self.job_prefix}{job_id}"
        
        job_info = {
            "job_id": job_id,
            "status": "pending",
            "progress": 0,
            "message": "Job created, waiting to be processed",
            "created_at": job_data.get("created_at"),
            "updated_at": job_data.get("created_at"),
            **job_data
        }
        
        await self.redis.hset(job_key, mapping=job_info)
        await self.redis.expire(job_key, settings.JOB_TIMEOUT)
        
        # Add to processing queue
        await self.redis.lpush(self.queue_name, job_id)
        
        logger.info(f"Created job {job_id}")
    
    async def get_job(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job information from Redis."""
        job_key = f"{self.job_prefix}{job_id}"
        job_data = await self.redis.hgetall(job_key)
        
        if not job_data:
            return None
        
        # Convert numeric fields
        if "progress" in job_data:
            job_data["progress"] = int(job_data["progress"])
        
        # Parse JSON fields
        for field in ["highlights", "error_details"]:
            if field in job_data and job_data[field]:
                try:
                    job_data[field] = json.loads(job_data[field])
                except json.JSONDecodeError:
                    pass
        
        return job_data
    
    async def update_job_status(
        self, 
        job_id: str, 
        status: str, 
        progress: int = None, 
        message: str = None,
        **kwargs
    ) -> None:
        """Update job status and progress."""
        job_key = f"{self.job_prefix}{job_id}"
        
        updates = {
            "status": status,
            "updated_at": kwargs.get("updated_at"),
        }
        
        if progress is not None:
            updates["progress"] = progress
        
        if message is not None:
            updates["message"] = message
        
        # Add any additional fields
        for key, value in kwargs.items():
            if key not in ["updated_at"]:
                if isinstance(value, (dict, list)):
                    updates[key] = json.dumps(value)
                else:
                    updates[key] = str(value)
        
        await self.redis.hset(job_key, mapping=updates)
        
        # Publish progress update
        progress_data = {
            "job_id": job_id,
            "status": status,
            "progress": progress or 0,
            "message": message or "",
        }
        await self.redis.publish(self.progress_channel, json.dumps(progress_data))
        
        logger.info(f"Updated job {job_id}: {status} ({progress}%)")
    
    async def complete_job(self, job_id: str, highlights: List[str]) -> None:
        """Mark job as completed with highlight URLs."""
        await self.update_job_status(
            job_id=job_id,
            status="completed",
            progress=100,
            message=f"Processing complete. Found {len(highlights)} highlights.",
            highlights=highlights,
        )
    
    async def fail_job(self, job_id: str, error_message: str, error_details: Dict = None) -> None:
        """Mark job as failed with error information."""
        await self.update_job_status(
            job_id=job_id,
            status="failed",
            message=f"Processing failed: {error_message}",
            error_message=error_message,
            error_details=error_details or {},
        )
    
    async def get_queue_length(self) -> int:
        """Get the current queue length."""
        return await self.redis.llen(self.queue_name)
    
    async def get_job_stats(self) -> Dict[str, int]:
        """Get job statistics."""
        # This is a simplified version - in production you'd want more sophisticated tracking
        queue_length = await self.get_queue_length()
        
        return {
            "queue_length": queue_length,
            "total_jobs": 0,  # Would track this separately
            "completed_jobs": 0,  # Would track this separately
            "failed_jobs": 0,  # Would track this separately
        }


def get_job_manager() -> RedisJobManager:
    """Get job manager instance."""
    return RedisJobManager()
