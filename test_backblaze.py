#!/usr/bin/env python3
"""Test Backblaze B2 bucket access"""

import sys
from dotenv import load_dotenv

# Load environment
load_dotenv('.env')

# Test bucket access
sys.path.append('backend')
from app.core.s3_client import get_s3_manager

try:
    s3_manager = get_s3_manager()
    print("✅ S3 Manager created")
    
    # Test bucket access
    s3_manager.s3.head_bucket(Bucket='MLhighlights')
    print("✅ MLhighlights bucket is accessible")
    
    # Test presigned URL generation
    test_url = s3_manager.generate_presigned_upload_url(
        object_key="test/sample.mp4",
        content_type="video/mp4"
    )
    print("✅ Presigned URL generation works")
    print(f"   Upload URL: {test_url['url'][:50]}...")
    
    print("\n🎉 Backblaze B2 integration is fully functional!")
    
except Exception as e:
    print(f"❌ Backblaze B2 error: {e}")
    sys.exit(1)
