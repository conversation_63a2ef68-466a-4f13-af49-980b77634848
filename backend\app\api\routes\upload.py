"""
Upload endpoints for video files.
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field, validator

from app.core.config import get_settings
from app.core.redis_client import get_job_manager
from app.core.s3_client import get_s3_manager

logger = logging.getLogger(__name__)
router = APIRouter()
settings = get_settings()


class PresignedUrlRequest(BaseModel):
    """Request model for presigned URL generation."""
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="MIME type of the file")
    file_size: int = Field(..., gt=0, description="File size in bytes")
    
    @validator('content_type')
    def validate_content_type(cls, v):
        """Validate that content type is a supported video format."""
        allowed_types = [
            'video/mp4',
            'video/avi', 
            'video/x-msvideo',
            'video/mov',
            'video/quicktime',
            'video/mkv',
            'video/x-matroska',
            'video/webm',
        ]
        if v not in allowed_types:
            raise ValueError(f'Unsupported content type: {v}')
        return v
    
    @validator('file_size')
    def validate_file_size(cls, v):
        """Validate file size is within limits."""
        if v > settings.MAX_FILE_SIZE:
            raise ValueError(f'File size {v} exceeds maximum allowed size {settings.MAX_FILE_SIZE}')
        return v
    
    @validator('filename')
    def validate_filename(cls, v):
        """Validate filename."""
        if not v or len(v.strip()) == 0:
            raise ValueError('Filename cannot be empty')
        
        # Check for valid extension
        allowed_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
        if not any(v.lower().endswith(ext) for ext in allowed_extensions):
            raise ValueError(f'Unsupported file extension. Allowed: {allowed_extensions}')
        
        return v.strip()


class UploadCallbackRequest(BaseModel):
    """Request model for upload completion callback."""
    job_id: str = Field(..., description="Job ID from presigned URL response")
    object_key: str = Field(..., description="S3 object key where file was uploaded")


class PresignedUrlResponse(BaseModel):
    """Response model for presigned URL."""
    upload_url: str = Field(..., description="URL for uploading the file")
    fields: Dict[str, str] = Field(..., description="Form fields for the upload")
    job_id: str = Field(..., description="Unique job identifier")
    expires_in: int = Field(..., description="URL expiration time in seconds")


@router.post("/presigned-url", response_model=PresignedUrlResponse)
async def generate_presigned_upload_url(
    request: PresignedUrlRequest,
    s3_manager: Any = Depends(get_s3_manager),
    job_manager: Any = Depends(get_job_manager)
):
    """
    Generate a presigned URL for direct upload to S3.
    
    This endpoint creates a job ID and returns a presigned URL that the frontend
    can use to upload the video file directly to S3.
    """
    try:
        # Generate unique job ID
        job_id = str(uuid.uuid4())
        
        # Create object key with job ID and original filename
        file_extension = request.filename.split('.')[-1].lower()
        object_key = f"uploads/{job_id}/{request.filename}"
        
        # Generate presigned URL
        presigned_data = s3_manager.generate_presigned_upload_url(
            object_key=object_key,
            content_type=request.content_type,
            expiration=3600  # 1 hour
        )
        
        # Create job in Redis
        job_data = {
            "filename": request.filename,
            "content_type": request.content_type,
            "file_size": request.file_size,
            "object_key": object_key,
            "created_at": datetime.utcnow().isoformat(),
        }
        
        await job_manager.create_job(job_id, job_data)
        
        logger.info(f"Generated presigned URL for job {job_id}, file: {request.filename}")
        
        return PresignedUrlResponse(
            upload_url=presigned_data["url"],
            fields=presigned_data["fields"],
            job_id=job_id,
            expires_in=3600
        )
        
    except Exception as e:
        logger.error(f"Failed to generate presigned URL: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate upload URL"
        )


@router.post("/callback")
async def upload_callback(
    request: UploadCallbackRequest,
    s3_manager: Any = Depends(get_s3_manager),
    job_manager: Any = Depends(get_job_manager)
):
    """
    Handle upload completion callback.
    
    This endpoint is called after the file has been successfully uploaded to S3.
    It verifies the upload and starts the video processing job.
    """
    try:
        # Get job information
        job_data = await job_manager.get_job(request.job_id)
        if not job_data:
            raise HTTPException(
                status_code=404,
                detail=f"Job {request.job_id} not found"
            )
        
        # Verify the object exists in S3
        if not s3_manager.object_exists(settings.INPUT_BUCKET, request.object_key):
            raise HTTPException(
                status_code=400,
                detail="Uploaded file not found in S3"
            )
        
        # Get object info to verify upload
        object_info = s3_manager.get_object_info(settings.INPUT_BUCKET, request.object_key)
        if not object_info:
            raise HTTPException(
                status_code=400,
                detail="Failed to verify uploaded file"
            )
        
        # Update job status to indicate upload is complete and processing can begin
        await job_manager.update_job_status(
            job_id=request.job_id,
            status="pending",
            progress=10,
            message="File uploaded successfully, queued for processing",
            object_key=request.object_key,
            uploaded_size=object_info["size"],
            updated_at=datetime.utcnow().isoformat()
        )
        
        logger.info(f"Upload callback processed for job {request.job_id}")
        
        return {
            "status": "success",
            "job_id": request.job_id,
            "message": "Upload confirmed, processing will begin shortly"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Upload callback failed for job {request.job_id}: {e}")
        
        # Try to update job status to failed
        try:
            await job_manager.fail_job(
                request.job_id,
                "Upload verification failed",
                {"error": str(e)}
            )
        except:
            pass
        
        raise HTTPException(
            status_code=500,
            detail="Upload callback processing failed"
        )
