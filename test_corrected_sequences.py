#!/usr/bin/env python3
"""Test the corrected multi-kill sequences"""

import sys
from dotenv import load_dotenv

# Load environment
load_dotenv('.env')

# Test the corrected sequences
sys.path.append('worker')
from processors.video_processor import VideoProcessor

def test_corrected_sequences():
    """Test the corrected multi-kill sequence definitions"""
    print("🎮 Testing Corrected Multi-Kill Sequences")
    print("=" * 50)
    
    try:
        processor = VideoProcessor()
        
        # Test that we now have 12 events (including savage)
        expected_events = 12
        actual_events = len(processor.highlight_phrases)
        
        if actual_events == expected_events:
            print(f"✅ All {expected_events} gaming events configured")
        else:
            print(f"❌ Expected {expected_events} events, got {actual_events}")
            return False
        
        # Test specific events and their configurations
        print("\n🔧 Event Configurations:")
        for event, config in processor.highlight_phrases.items():
            start_offset, end_offset, is_sequence = config
            print(f"   {event}: {start_offset}s to +{end_offset}s, sequence={is_sequence}")
        
        # Test multi-kill sequences
        print("\n🎯 Multi-Kill Sequences:")
        expected_sequences = {
            "double_kill": "Should have 2 possible sequences",
            "triple_kill": "Should have single sequence",
            "mega_kill": "Should have single sequence", 
            "maniac": "Should have single sequence",
            "savage": "Should have single sequence",
            "godlike": "Should have single sequence",
            "unstoppable": "Should have single sequence"
        }
        
        for sequence_name, description in expected_sequences.items():
            if sequence_name in processor.multi_kill_sequences:
                sequence_def = processor.multi_kill_sequences[sequence_name]
                
                if sequence_name == "double_kill":
                    # Should have 2 possible sequences
                    if isinstance(sequence_def, list) and len(sequence_def) == 2:
                        print(f"✅ {sequence_name}: {description}")
                        print(f"     Option 1: {' → '.join(sequence_def[0])}")
                        print(f"     Option 2: {' → '.join(sequence_def[1])}")
                    else:
                        print(f"❌ {sequence_name}: Expected 2 sequences, got {sequence_def}")
                        return False
                else:
                    # Should have single sequence
                    if isinstance(sequence_def, list) and not isinstance(sequence_def[0], list):
                        print(f"✅ {sequence_name}: {' → '.join(sequence_def)}")
                    else:
                        print(f"❌ {sequence_name}: Expected single sequence, got {sequence_def}")
                        return False
            else:
                print(f"❌ {sequence_name}: Not found in sequences")
                return False
        
        # Test sample transcript with corrected sequences
        print("\n🧪 Testing Sample Multi-Kill Sequence:")
        
        sample_transcript = {
            "segments": [
                {
                    "words": [
                        {"word": "You", "start": 10.0, "end": 10.2},
                        {"word": "have", "start": 10.2, "end": 10.4},
                        {"word": "slain", "start": 10.4, "end": 10.7},
                        {"word": "an", "start": 10.7, "end": 10.8},
                        {"word": "enemy", "start": 10.8, "end": 11.2},
                    ]
                },
                {
                    "words": [
                        {"word": "Double", "start": 15.0, "end": 15.3},
                        {"word": "kill", "start": 15.3, "end": 15.6},
                    ]
                },
                {
                    "words": [
                        {"word": "Triple", "start": 20.0, "end": 20.3},
                        {"word": "kill", "start": 20.3, "end": 20.6},
                    ]
                },
                {
                    "words": [
                        {"word": "Mega", "start": 25.0, "end": 25.3},
                        {"word": "kill", "start": 25.3, "end": 25.6},
                    ]
                },
                {
                    "words": [
                        {"word": "Maniac", "start": 30.0, "end": 30.5},
                    ]
                },
                {
                    "words": [
                        {"word": "Savage", "start": 35.0, "end": 35.5},
                    ]
                }
            ]
        }
        
        import asyncio
        
        async def test_detection():
            highlights = await processor.detect_highlights(sample_transcript)
            return highlights
        
        highlights = asyncio.run(test_detection())
        
        print(f"✅ Detected {len(highlights)} highlight ranges:")
        for i, (start, end) in enumerate(highlights):
            duration = end - start
            print(f"   Clip {i+1}: {start:.1f}s - {end:.1f}s ({duration:.1f}s duration)")
        
        # Verify savage event is included
        if "savage" in processor.highlight_phrases:
            savage_config = processor.highlight_phrases["savage"]
            print(f"✅ Savage event: {savage_config[0]}s to +{savage_config[1]}s, sequence={savage_config[2]}")
        else:
            print("❌ Savage event not found")
            return False
        
        print("\n🎉 All corrected multi-kill sequences are working perfectly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing sequences: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the corrected sequence test"""
    success = test_corrected_sequences()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 CORRECTED SEQUENCES VERIFIED!")
        print("\n📋 Summary:")
        print("   • 12 gaming events configured (including savage)")
        print("   • Double kill: 2 possible sequences")
        print("   • Triple → Savage: Progressive sequences")
        print("   • Enhanced timing for epic moments")
        print("\n🚀 Ready for epic gaming highlight extraction!")
    else:
        print("\n❌ Some sequence tests failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
