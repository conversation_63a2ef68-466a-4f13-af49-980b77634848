'use client';

import { useState } from 'react';
import VideoUploader from '@/components/VideoUploader';
import JobStatus from '@/components/JobStatus';
import HighlightsList from '@/components/HighlightsList';
import { Upload, Zap, Download, Play } from 'lucide-react';

export default function HomePage() {
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<'idle' | 'uploading' | 'processing' | 'completed' | 'error'>('idle');
  const [highlights, setHighlights] = useState<string[]>([]);

  const handleUploadStart = () => {
    setJobStatus('uploading');
    setCurrentJobId(null);
    setHighlights([]);
  };

  const handleUploadComplete = (jobId: string) => {
    setCurrentJobId(jobId);
    setJobStatus('processing');
  };

  const handleJobComplete = (highlightUrls: string[]) => {
    setJobStatus('completed');
    setHighlights(highlightUrls);
  };

  const handleJobError = () => {
    setJobStatus('error');
  };

  const resetState = () => {
    setCurrentJobId(null);
    setJobStatus('idle');
    setHighlights([]);
  };

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h2 className="text-4xl font-bold text-gray-900 sm:text-5xl">
          Extract Gaming Highlights
          <span className="text-primary-600"> Automatically</span>
        </h2>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto text-balance">
          Upload your gaming videos and let AI find the best moments. 
          Our system detects kills, shutdowns, and epic plays to create highlight clips.
        </p>
      </div>

      {/* Features */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="card text-center">
          <Upload className="w-8 h-8 text-primary-600 mx-auto mb-3" />
          <h3 className="font-semibold text-gray-900 mb-2">Easy Upload</h3>
          <p className="text-sm text-gray-600">Drag and drop your video files</p>
        </div>
        <div className="card text-center">
          <Zap className="w-8 h-8 text-primary-600 mx-auto mb-3" />
          <h3 className="font-semibold text-gray-900 mb-2">AI Processing</h3>
          <p className="text-sm text-gray-600">Whisper AI detects highlight moments</p>
        </div>
        <div className="card text-center">
          <Download className="w-8 h-8 text-primary-600 mx-auto mb-3" />
          <h3 className="font-semibold text-gray-900 mb-2">Download Clips</h3>
          <p className="text-sm text-gray-600">Get individual highlight videos</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Upload Section */}
        <div className="space-y-6">
          <VideoUploader
            onUploadStart={handleUploadStart}
            onUploadComplete={handleUploadComplete}
            onError={handleJobError}
            disabled={jobStatus === 'uploading' || jobStatus === 'processing'}
          />
          
          {jobStatus !== 'idle' && (
            <button
              onClick={resetState}
              className="btn-secondary w-full"
              disabled={jobStatus === 'uploading' || jobStatus === 'processing'}
            >
              Upload Another Video
            </button>
          )}
        </div>

        {/* Status and Results Section */}
        <div className="space-y-6">
          {currentJobId && (
            <JobStatus
              jobId={currentJobId}
              onComplete={handleJobComplete}
              onError={handleJobError}
            />
          )}
          
          {highlights.length > 0 && (
            <HighlightsList highlights={highlights} />
          )}
          
          {jobStatus === 'error' && (
            <div className="card border-error-200 bg-error-50">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-error-100 rounded-full flex items-center justify-center">
                  <span className="text-error-600 text-sm">!</span>
                </div>
                <div>
                  <h3 className="font-medium text-error-900">Processing Error</h3>
                  <p className="text-sm text-error-700">
                    Something went wrong while processing your video. Please try again.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Info Section */}
      <div className="card bg-primary-50 border-primary-200">
        <div className="flex items-start space-x-3">
          <Play className="w-6 h-6 text-primary-600 mt-1" />
          <div>
            <h3 className="font-medium text-primary-900 mb-2">Supported Highlights</h3>
            <p className="text-sm text-primary-800 mb-3">
              Our AI detects these gaming moments and creates clips with optimal timing:
            </p>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 text-sm">
              <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">Double Kill</span>
              <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">First Blood</span>
              <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">Triple Kill</span>
              <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">Mega Kill</span>
              <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">Monster Kill</span>
              <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">Maniac</span>
              <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">Godlike</span>
              <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">Legendary</span>
              <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">Unstoppable</span>
              <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">Killing Spree</span>
              <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">Enemy Slain</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
