name: Security Scanning

on:
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  # Dependency vulnerability scanning
  dependency-scan:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        component: [frontend, backend, worker]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js (for frontend)
      if: matrix.component == 'frontend'
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Setup Python (for backend/worker)
      if: matrix.component != 'frontend'
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies and run audit (frontend)
      if: matrix.component == 'frontend'
      working-directory: ./frontend
      run: |
        npm ci
        npm audit --audit-level=moderate
    
    - name: Install dependencies and run safety check (Python)
      if: matrix.component != 'frontend'
      working-directory: ./${{ matrix.component }}
      run: |
        python -m pip install --upgrade pip
        pip install safety
        pip install -r requirements.txt
        safety check --json --output safety-report.json || true
    
    - name: Upload safety report
      if: matrix.component != 'frontend'
      uses: actions/upload-artifact@v3
      with:
        name: safety-report-${{ matrix.component }}
        path: ./${{ matrix.component }}/safety-report.json

  # Container image scanning
  container-scan:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [frontend, backend, worker]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Build Docker image
      run: |
        docker build -t test-image:${{ matrix.service }} ./${{ matrix.service }}
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'test-image:${{ matrix.service }}'
        format: 'sarif'
        output: 'trivy-${{ matrix.service }}.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-${{ matrix.service }}.sarif'
        category: 'container-${{ matrix.service }}'

  # Code quality and security analysis
  codeql-analysis:
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    
    strategy:
      matrix:
        language: ['javascript', 'python']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: ${{ matrix.language }}
        queries: security-extended,security-and-quality
    
    - name: Autobuild
      uses: github/codeql-action/autobuild@v2
    
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
      with:
        category: "/language:${{ matrix.language }}"

  # Secrets scanning
  secrets-scan:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Fetch all history for all branches
    
    - name: Run TruffleHog OSS
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD
        extra_args: --debug --only-verified
