'use client';

import { useState } from 'react';
import { Download, Play, Eye, Copy, Check } from 'lucide-react';

interface HighlightsListProps {
  highlights: string[];
}

export default function HighlightsList({ highlights }: HighlightsListProps) {
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);

  const handleCopyUrl = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopiedUrl(url);
      setTimeout(() => setCopiedUrl(null), 2000);
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  };

  const getClipName = (url: string, index: number) => {
    // Extract filename from URL or use index
    const urlParts = url.split('/');
    const filename = urlParts[urlParts.length - 1];
    return filename || `Highlight ${index + 1}`;
  };

  const formatFileSize = (url: string) => {
    // This would typically come from the API, but for demo purposes
    return 'Unknown size';
  };

  if (highlights.length === 0) {
    return null;
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Highlight Clips ({highlights.length})
        </h3>
        <div className="text-sm text-gray-500">
          Ready to download
        </div>
      </div>

      <div className="space-y-3">
        {highlights.map((url, index) => (
          <div
            key={index}
            className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                <Play className="w-5 h-5 text-primary-600" />
              </div>
              
              <div className="flex-1 min-w-0">
                <p className="font-medium text-gray-900 truncate">
                  {getClipName(url, index)}
                </p>
                <p className="text-sm text-gray-500">
                  Clip {index + 1} • {formatFileSize(url)}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2 ml-4">
              {/* Preview Button */}
              <button
                onClick={() => window.open(url, '_blank')}
                className="p-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                title="Preview clip"
              >
                <Eye className="w-4 h-4" />
              </button>

              {/* Copy URL Button */}
              <button
                onClick={() => handleCopyUrl(url)}
                className="p-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                title="Copy URL"
              >
                {copiedUrl === url ? (
                  <Check className="w-4 h-4 text-success-600" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </button>

              {/* Download Button */}
              <a
                href={url}
                download={getClipName(url, index)}
                className="p-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                title="Download clip"
              >
                <Download className="w-4 h-4" />
              </a>
            </div>
          </div>
        ))}
      </div>

      {/* Bulk Actions */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={() => {
              highlights.forEach((url, index) => {
                const link = document.createElement('a');
                link.href = url;
                link.download = getClipName(url, index);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              });
            }}
            className="btn-primary flex items-center justify-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Download All Clips</span>
          </button>
          
          <button
            onClick={() => {
              const urlsText = highlights.join('\n');
              navigator.clipboard.writeText(urlsText);
            }}
            className="btn-secondary flex items-center justify-center space-x-2"
          >
            <Copy className="w-4 h-4" />
            <span>Copy All URLs</span>
          </button>
        </div>
      </div>

      {/* Tips */}
      <div className="mt-4 p-3 bg-primary-50 border border-primary-200 rounded-lg">
        <p className="text-sm text-primary-800">
          <strong>💡 Tip:</strong> Right-click any clip and select "Save video as..." to download with a custom name.
          Clips are automatically trimmed with optimal timing around detected highlights.
        </p>
      </div>
    </div>
  );
}
