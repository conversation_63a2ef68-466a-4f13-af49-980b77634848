'use client';

import { useEffect, useState } from 'react';
import { Clock, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { getJobStatus } from '@/lib/api';

interface JobStatusProps {
  jobId: string;
  onComplete: (highlights: string[]) => void;
  onError: () => void;
}

interface JobStatusData {
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  highlights?: string[];
  created_at: string;
  updated_at: string;
}

export default function JobStatus({ jobId, onComplete, onError }: JobStatusProps) {
  const [jobData, setJobData] = useState<JobStatusData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    const fetchStatus = async () => {
      try {
        const data = await getJobStatus(jobId);
        setJobData(data);
        setError(null);

        if (data.status === 'completed' && data.highlights) {
          onComplete(data.highlights);
          clearInterval(intervalId);
        } else if (data.status === 'failed') {
          onError();
          clearInterval(intervalId);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch status');
        onError();
        clearInterval(intervalId);
      } finally {
        setLoading(false);
      }
    };

    // Initial fetch
    fetchStatus();

    // Poll every 2 seconds if job is not completed
    intervalId = setInterval(() => {
      if (jobData?.status === 'completed' || jobData?.status === 'failed') {
        clearInterval(intervalId);
        return;
      }
      fetchStatus();
    }, 2000);

    return () => clearInterval(intervalId);
  }, [jobId, jobData?.status, onComplete, onError]);

  if (loading && !jobData) {
    return (
      <div className="card">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 text-primary-600 animate-spin" />
          <div>
            <h3 className="font-medium text-gray-900">Initializing...</h3>
            <p className="text-sm text-gray-600">Setting up your video processing job</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card border-error-200 bg-error-50">
        <div className="flex items-center space-x-3">
          <AlertCircle className="w-6 h-6 text-error-600" />
          <div>
            <h3 className="font-medium text-error-900">Status Error</h3>
            <p className="text-sm text-error-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!jobData) return null;

  const getStatusIcon = () => {
    switch (jobData.status) {
      case 'pending':
        return <Clock className="w-6 h-6 text-warning-600" />;
      case 'processing':
        return <Loader2 className="w-6 h-6 text-primary-600 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-6 h-6 text-success-600" />;
      case 'failed':
        return <AlertCircle className="w-6 h-6 text-error-600" />;
      default:
        return <Clock className="w-6 h-6 text-gray-600" />;
    }
  };

  const getStatusColor = () => {
    switch (jobData.status) {
      case 'pending':
        return 'border-warning-200 bg-warning-50';
      case 'processing':
        return 'border-primary-200 bg-primary-50';
      case 'completed':
        return 'border-success-200 bg-success-50';
      case 'failed':
        return 'border-error-200 bg-error-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getStatusText = () => {
    switch (jobData.status) {
      case 'pending':
        return 'Queued for Processing';
      case 'processing':
        return 'Processing Video';
      case 'completed':
        return 'Processing Complete';
      case 'failed':
        return 'Processing Failed';
      default:
        return 'Unknown Status';
    }
  };

  const getProgressSteps = () => {
    const steps = [
      { name: 'Queued', completed: jobData.progress >= 0 },
      { name: 'Downloading', completed: jobData.progress >= 20 },
      { name: 'Audio Analysis', completed: jobData.progress >= 40 },
      { name: 'Highlight Detection', completed: jobData.progress >= 60 },
      { name: 'Creating Clips', completed: jobData.progress >= 80 },
      { name: 'Uploading Results', completed: jobData.progress >= 100 },
    ];

    return steps;
  };

  return (
    <div className={`card ${getStatusColor()}`}>
      <div className="space-y-4">
        {/* Status Header */}
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <div className="flex-1">
            <h3 className="font-medium text-gray-900">{getStatusText()}</h3>
            <p className="text-sm text-gray-600">{jobData.message}</p>
          </div>
          <div className="text-right">
            <p className="text-sm font-medium text-gray-900">Job ID</p>
            <p className="text-xs text-gray-500 font-mono">{jobId.slice(0, 8)}...</p>
          </div>
        </div>

        {/* Progress Bar */}
        {(jobData.status === 'processing' || jobData.status === 'pending') && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Progress</span>
              <span className="text-gray-900 font-medium">{jobData.progress}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ width: `${jobData.progress}%` }}
              />
            </div>
          </div>
        )}

        {/* Processing Steps */}
        {jobData.status === 'processing' && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700">Processing Steps:</p>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              {getProgressSteps().map((step, index) => (
                <div
                  key={index}
                  className={`text-xs px-2 py-1 rounded-full text-center ${
                    step.completed
                      ? 'bg-success-100 text-success-800'
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  {step.name}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Completion Info */}
        {jobData.status === 'completed' && jobData.highlights && (
          <div className="bg-success-100 border border-success-200 rounded-lg p-3">
            <p className="text-sm text-success-800">
              <strong>Success!</strong> Found {jobData.highlights.length} highlight{jobData.highlights.length !== 1 ? 's' : ''} in your video.
            </p>
          </div>
        )}

        {/* Timestamps */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>Started: {new Date(jobData.created_at).toLocaleString()}</p>
          <p>Updated: {new Date(jobData.updated_at).toLocaleString()}</p>
        </div>
      </div>
    </div>
  );
}
