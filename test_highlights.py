#!/usr/bin/env python3
"""Test enhanced highlight detection"""

import sys
from dotenv import load_dotenv

# Load environment
load_dotenv('.env')

# Test highlight detection
sys.path.append('worker')
from processors.video_processor import VideoProcessor

try:
    processor = VideoProcessor()
    print("✅ Video processor created")
    
    # Test sample transcript with gaming events
    sample_transcript = {
        "segments": [
            {
                "words": [
                    {"word": "You", "start": 10.0, "end": 10.2},
                    {"word": "have", "start": 10.2, "end": 10.4},
                    {"word": "slain", "start": 10.4, "end": 10.7},
                    {"word": "an", "start": 10.7, "end": 10.8},
                    {"word": "enemy", "start": 10.8, "end": 11.2},
                ]
            },
            {
                "words": [
                    {"word": "Double", "start": 15.0, "end": 15.3},
                    {"word": "kill", "start": 15.3, "end": 15.6},
                ]
            },
            {
                "words": [
                    {"word": "Triple", "start": 20.0, "end": 20.3},
                    {"word": "kill", "start": 20.3, "end": 20.6},
                ]
            },
            {
                "words": [
                    {"word": "Maniac", "start": 25.0, "end": 25.5},
                ]
            }
        ]
    }
    
    print("✅ Testing highlight detection with sample transcript...")
    
    # This would normally be async, but we'll test the sync parts
    import asyncio
    
    async def test_detection():
        highlights = await processor.detect_highlights(sample_transcript)
        return highlights
    
    highlights = asyncio.run(test_detection())
    
    print(f"✅ Detected {len(highlights)} highlight ranges:")
    for i, (start, end) in enumerate(highlights):
        duration = end - start
        print(f"   Clip {i+1}: {start:.1f}s - {end:.1f}s ({duration:.1f}s duration)")
    
    # Test specific configurations
    print("\n✅ Gaming event configurations:")
    for event, config in processor.highlight_phrases.items():
        start_offset, end_offset, is_sequence = config
        print(f"   {event}: {start_offset}s to +{end_offset}s, sequence={is_sequence}")
    
    print("\n✅ Multi-kill sequences:")
    for sequence, events in processor.multi_kill_sequences.items():
        print(f"   {sequence}: {' → '.join(events)}")
    
    print("\n🎉 Enhanced highlight detection is working perfectly!")
    
except Exception as e:
    print(f"❌ Highlight detection error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
