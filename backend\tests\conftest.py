"""
Pytest configuration and fixtures for backend tests.
"""

import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator, Generator
from unittest.mock import AsyncMock, MagicMock

from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import app
from app.core.config import get_settings
from app.core.redis_client import get_redis_client, get_job_manager
from app.core.s3_client import get_s3_client, get_s3_manager


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def settings():
    """Get test settings."""
    return get_settings()


@pytest.fixture
def mock_redis_client():
    """Mock Redis client for testing."""
    mock_redis = AsyncMock()
    mock_redis.ping.return_value = True
    mock_redis.hgetall.return_value = {}
    mock_redis.hset.return_value = True
    mock_redis.expire.return_value = True
    mock_redis.lpush.return_value = 1
    mock_redis.llen.return_value = 0
    mock_redis.publish.return_value = 1
    return mock_redis


@pytest.fixture
def mock_s3_client():
    """Mock S3 client for testing."""
    mock_s3 = MagicMock()
    mock_s3.list_buckets.return_value = {"Buckets": []}
    mock_s3.head_bucket.return_value = {}
    mock_s3.generate_presigned_post.return_value = {
        "url": "https://test-bucket.s3.amazonaws.com/",
        "fields": {
            "key": "test-key",
            "policy": "test-policy",
            "x-amz-algorithm": "AWS4-HMAC-SHA256",
            "x-amz-credential": "test-credential",
            "x-amz-date": "20231201T000000Z",
            "x-amz-signature": "test-signature"
        }
    }
    mock_s3.head_object.return_value = {
        "ContentLength": 1024,
        "LastModified": "2023-12-01T00:00:00Z",
        "ContentType": "video/mp4",
        "ETag": "test-etag"
    }
    return mock_s3


@pytest.fixture
def mock_job_manager(mock_redis_client):
    """Mock job manager for testing."""
    from app.core.redis_client import RedisJobManager
    
    job_manager = RedisJobManager(mock_redis_client)
    return job_manager


@pytest.fixture
def mock_s3_manager(mock_s3_client):
    """Mock S3 manager for testing."""
    from app.core.s3_client import S3Manager
    
    s3_manager = S3Manager(mock_s3_client)
    return s3_manager


@pytest.fixture
def client(mock_redis_client, mock_s3_client, mock_job_manager, mock_s3_manager):
    """Test client with mocked dependencies."""
    
    # Override dependencies
    app.dependency_overrides[get_redis_client] = lambda: mock_redis_client
    app.dependency_overrides[get_s3_client] = lambda: mock_s3_client
    app.dependency_overrides[get_job_manager] = lambda: mock_job_manager
    app.dependency_overrides[get_s3_manager] = lambda: mock_s3_manager
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clear overrides
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def async_client(mock_redis_client, mock_s3_client, mock_job_manager, mock_s3_manager):
    """Async test client with mocked dependencies."""
    
    # Override dependencies
    app.dependency_overrides[get_redis_client] = lambda: mock_redis_client
    app.dependency_overrides[get_s3_client] = lambda: mock_s3_client
    app.dependency_overrides[get_job_manager] = lambda: mock_job_manager
    app.dependency_overrides[get_s3_manager] = lambda: mock_s3_manager
    
    async with AsyncClient(app=app, base_url="http://test") as async_test_client:
        yield async_test_client
    
    # Clear overrides
    app.dependency_overrides.clear()


@pytest.fixture
def sample_job_data():
    """Sample job data for testing."""
    return {
        "job_id": "test-job-123",
        "filename": "test_video.mp4",
        "content_type": "video/mp4",
        "file_size": 1024000,
        "object_key": "uploads/test-job-123/test_video.mp4",
        "status": "pending",
        "progress": 0,
        "message": "Job created",
        "created_at": "2023-12-01T00:00:00Z",
        "updated_at": "2023-12-01T00:00:00Z"
    }


@pytest.fixture
def sample_upload_request():
    """Sample upload request data."""
    return {
        "filename": "test_video.mp4",
        "content_type": "video/mp4",
        "file_size": 1024000
    }


@pytest.fixture
def sample_callback_request():
    """Sample upload callback request data."""
    return {
        "job_id": "test-job-123",
        "object_key": "uploads/test-job-123/test_video.mp4"
    }
