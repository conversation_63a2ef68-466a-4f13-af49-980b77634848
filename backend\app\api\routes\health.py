"""
Health check endpoints.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException
from app.core.redis_client import get_redis_client, get_job_manager
from app.core.s3_client import get_s3_client
from app.core.config import get_settings

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/health")
async def health_check():
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "video-highlight-extractor-api"
    }


@router.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with service dependencies."""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "video-highlight-extractor-api",
        "checks": {}
    }
    
    # Check Redis connection
    try:
        redis_client = get_redis_client()
        await redis_client.ping()
        health_status["checks"]["redis"] = {
            "status": "healthy",
            "message": "Connected successfully"
        }
    except Exception as e:
        health_status["checks"]["redis"] = {
            "status": "unhealthy",
            "message": f"Connection failed: {str(e)}"
        }
        health_status["status"] = "unhealthy"
    
    # Check S3 connection
    try:
        s3_client = get_s3_client()
        s3_client.list_buckets()
        health_status["checks"]["s3"] = {
            "status": "healthy",
            "message": "Connected successfully"
        }
    except Exception as e:
        health_status["checks"]["s3"] = {
            "status": "unhealthy",
            "message": f"Connection failed: {str(e)}"
        }
        health_status["status"] = "unhealthy"
    
    # Check job queue
    try:
        job_manager = get_job_manager()
        queue_length = await job_manager.get_queue_length()
        health_status["checks"]["job_queue"] = {
            "status": "healthy",
            "message": f"Queue length: {queue_length}"
        }
    except Exception as e:
        health_status["checks"]["job_queue"] = {
            "status": "unhealthy",
            "message": f"Queue check failed: {str(e)}"
        }
        health_status["status"] = "unhealthy"
    
    if health_status["status"] == "unhealthy":
        raise HTTPException(status_code=503, detail=health_status)
    
    return health_status


@router.get("/stats")
async def get_system_stats():
    """Get system statistics."""
    try:
        job_manager = get_job_manager()
        stats = await job_manager.get_job_stats()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "job_stats": stats,
            "system_info": {
                "service": "video-highlight-extractor-api",
                "version": "1.0.0",
            }
        }
    except Exception as e:
        logger.error(f"Failed to get system stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve system statistics")
