version: '3.8'

services:
  # Frontend - Next.js Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - api
    networks:
      - app-network

  # Backend API - FastAPI
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - BACKBLAZE_ACCESS_KEY_ID=${BACKBLAZE_ACCESS_KEY_ID:-test}
      - BACKBLAZE_SECRET_ACCESS_KEY=${BACKBLAZE_SECRET_ACCESS_KEY:-test}
      - BACKBLAZE_REGION=${BACKBLAZE_REGION:-us-east-1}
      - BACKBLAZE_ENDPOINT_URL=${BACKBLAZE_ENDPOINT_URL:-http://localstack:4566}
      - INPUT_BUCKET=${INPUT_BUCKET:-video-uploads}
      - OUTPUT_BUCKET=${OUTPUT_BUCKET:-video-highlights}
      - FRONTEND_URL=http://localhost:3000
    volumes:
      - ./backend:/app
    depends_on:
      - redis
      - localstack
    networks:
      - app-network

  # Worker Service - Video Processing
  worker:
    build:
      context: ./worker
      dockerfile: Dockerfile
    environment:
      - REDIS_URL=redis://redis:6379/0
      - BACKBLAZE_ACCESS_KEY_ID=${BACKBLAZE_ACCESS_KEY_ID:-test}
      - BACKBLAZE_SECRET_ACCESS_KEY=${BACKBLAZE_SECRET_ACCESS_KEY:-test}
      - BACKBLAZE_REGION=${BACKBLAZE_REGION:-us-east-1}
      - BACKBLAZE_ENDPOINT_URL=${BACKBLAZE_ENDPOINT_URL:-http://localstack:4566}
      - INPUT_BUCKET=${INPUT_BUCKET:-video-uploads}
      - OUTPUT_BUCKET=${OUTPUT_BUCKET:-video-highlights}
      - WHISPER_MODEL=medium
      - TEMP_DIR=/tmp/video_processing
    volumes:
      - ./worker:/app
      - worker_temp:/tmp/video_processing
    depends_on:
      - redis
      - localstack
    networks:
      - app-network
    deploy:
      replicas: 2

  # Redis - Job Queue and Pub/Sub
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network

  # LocalStack - S3 Mock for Development
  localstack:
    image: localstack/localstack:latest
    ports:
      - "4566:4566"
    environment:
      - SERVICES=s3
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - localstack_data:/tmp/localstack
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - app-network

  # Redis Commander - Redis GUI (Development)
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - app-network
    profiles:
      - dev

volumes:
  redis_data:
  localstack_data:
  worker_temp:

networks:
  app-network:
    driver: bridge
