"""
Tests for upload endpoints.
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient


def test_generate_presigned_url_success(client: TestClient, sample_upload_request, mock_s3_manager, mock_job_manager):
    """Test successful presigned URL generation."""
    # Mock S3 manager response
    mock_s3_manager.generate_presigned_upload_url.return_value = {
        "url": "https://test-bucket.s3.amazonaws.com/",
        "fields": {
            "key": "uploads/test-job/test_video.mp4",
            "policy": "test-policy",
            "x-amz-algorithm": "AWS4-HMAC-SHA256",
            "x-amz-credential": "test-credential",
            "x-amz-date": "20231201T000000Z",
            "x-amz-signature": "test-signature"
        }
    }
    
    # Mock job manager
    mock_job_manager.create_job = MagicMock()
    
    with patch('uuid.uuid4') as mock_uuid:
        mock_uuid.return_value.hex = "test-job-123"
        mock_uuid.return_value.__str__ = lambda x: "test-job-123"
        
        response = client.post("/upload/presigned-url", json=sample_upload_request)
    
    assert response.status_code == 200
    data = response.json()
    
    assert "upload_url" in data
    assert "fields" in data
    assert "job_id" in data
    assert data["expires_in"] == 3600
    assert data["upload_url"] == "https://test-bucket.s3.amazonaws.com/"


def test_generate_presigned_url_invalid_content_type(client: TestClient):
    """Test presigned URL generation with invalid content type."""
    invalid_request = {
        "filename": "test_file.txt",
        "content_type": "text/plain",
        "file_size": 1024
    }
    
    response = client.post("/upload/presigned-url", json=invalid_request)
    
    assert response.status_code == 422
    data = response.json()
    assert "detail" in data


def test_generate_presigned_url_invalid_file_size(client: TestClient):
    """Test presigned URL generation with invalid file size."""
    invalid_request = {
        "filename": "test_video.mp4",
        "content_type": "video/mp4",
        "file_size": 600 * 1024 * 1024  # 600MB - exceeds limit
    }
    
    response = client.post("/upload/presigned-url", json=invalid_request)
    
    assert response.status_code == 422
    data = response.json()
    assert "detail" in data


def test_generate_presigned_url_invalid_filename(client: TestClient):
    """Test presigned URL generation with invalid filename."""
    invalid_request = {
        "filename": "test_file.txt",  # Wrong extension
        "content_type": "video/mp4",
        "file_size": 1024
    }
    
    response = client.post("/upload/presigned-url", json=invalid_request)
    
    assert response.status_code == 422
    data = response.json()
    assert "detail" in data


@pytest.mark.asyncio
async def test_upload_callback_success(async_client, sample_callback_request, mock_job_manager, mock_s3_manager):
    """Test successful upload callback."""
    # Mock job manager to return job data
    mock_job_manager.get_job.return_value = {
        "job_id": "test-job-123",
        "filename": "test_video.mp4",
        "status": "pending"
    }
    
    # Mock S3 manager
    mock_s3_manager.object_exists.return_value = True
    mock_s3_manager.get_object_info.return_value = {
        "size": 1024000,
        "last_modified": "2023-12-01T00:00:00Z",
        "content_type": "video/mp4",
        "etag": "test-etag"
    }
    
    # Mock job manager update
    mock_job_manager.update_job_status = MagicMock()
    
    response = await async_client.post("/upload/callback", json=sample_callback_request)
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "success"
    assert data["job_id"] == "test-job-123"
    assert "Upload confirmed" in data["message"]


@pytest.mark.asyncio
async def test_upload_callback_job_not_found(async_client, sample_callback_request, mock_job_manager):
    """Test upload callback with non-existent job."""
    # Mock job manager to return None
    mock_job_manager.get_job.return_value = None
    
    response = await async_client.post("/upload/callback", json=sample_callback_request)
    
    assert response.status_code == 404
    data = response.json()
    assert "not found" in data["detail"]


@pytest.mark.asyncio
async def test_upload_callback_file_not_found(async_client, sample_callback_request, mock_job_manager, mock_s3_manager):
    """Test upload callback when file doesn't exist in S3."""
    # Mock job manager to return job data
    mock_job_manager.get_job.return_value = {
        "job_id": "test-job-123",
        "filename": "test_video.mp4",
        "status": "pending"
    }
    
    # Mock S3 manager to return file not found
    mock_s3_manager.object_exists.return_value = False
    
    response = await async_client.post("/upload/callback", json=sample_callback_request)
    
    assert response.status_code == 400
    data = response.json()
    assert "not found in S3" in data["detail"]


def test_presigned_url_request_validation():
    """Test PresignedUrlRequest validation."""
    from app.api.routes.upload import PresignedUrlRequest
    
    # Valid request
    valid_data = {
        "filename": "test_video.mp4",
        "content_type": "video/mp4",
        "file_size": 1024000
    }
    request = PresignedUrlRequest(**valid_data)
    assert request.filename == "test_video.mp4"
    
    # Invalid content type
    with pytest.raises(ValueError):
        PresignedUrlRequest(
            filename="test_video.mp4",
            content_type="text/plain",
            file_size=1024000
        )
    
    # Invalid file size
    with pytest.raises(ValueError):
        PresignedUrlRequest(
            filename="test_video.mp4",
            content_type="video/mp4",
            file_size=600 * 1024 * 1024  # Too large
        )
    
    # Invalid filename
    with pytest.raises(ValueError):
        PresignedUrlRequest(
            filename="test_file.txt",
            content_type="video/mp4",
            file_size=1024000
        )
