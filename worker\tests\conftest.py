"""
Pytest configuration and fixtures for worker tests.
"""

import asyncio
import pytest
import tempfile
import os
from typing import Generator
from unittest.mock import AsyncMock, MagicMock, patch

from core.config import get_settings


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def settings():
    """Get test settings."""
    return get_settings()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield tmp_dir


@pytest.fixture
def mock_redis_client():
    """Mock Redis client for testing."""
    mock_redis = AsyncMock()
    mock_redis.ping.return_value = True
    mock_redis.hgetall.return_value = {}
    mock_redis.hset.return_value = True
    mock_redis.expire.return_value = True
    mock_redis.lpush.return_value = 1
    mock_redis.llen.return_value = 0
    mock_redis.publish.return_value = 1
    mock_redis.brpop.return_value = None
    mock_redis.close.return_value = None
    return mock_redis


@pytest.fixture
def mock_s3_manager():
    """Mock S3 manager for testing."""
    mock_s3 = MagicMock()
    mock_s3.ensure_buckets_exist.return_value = None
    mock_s3.download_file.return_value = None
    mock_s3.upload_file.return_value = None
    mock_s3.object_exists.return_value = True
    mock_s3.get_object_size.return_value = 1024000
    return mock_s3


@pytest.fixture
def mock_whisper_model():
    """Mock Whisper model for testing."""
    mock_model = MagicMock()
    mock_model.transcribe.return_value = {
        "text": "This is a test transcript with double kill and triple kill",
        "segments": [
            {
                "start": 0.0,
                "end": 5.0,
                "text": "This is a test transcript",
                "words": [
                    {"word": "This", "start": 0.0, "end": 0.5},
                    {"word": "is", "start": 0.5, "end": 0.7},
                    {"word": "a", "start": 0.7, "end": 0.8},
                    {"word": "test", "start": 0.8, "end": 1.2},
                    {"word": "transcript", "start": 1.2, "end": 2.0}
                ]
            },
            {
                "start": 10.0,
                "end": 15.0,
                "text": "with double kill and triple kill",
                "words": [
                    {"word": "with", "start": 10.0, "end": 10.3},
                    {"word": "double", "start": 10.3, "end": 10.8},
                    {"word": "kill", "start": 10.8, "end": 11.2},
                    {"word": "and", "start": 11.2, "end": 11.4},
                    {"word": "triple", "start": 11.4, "end": 11.9},
                    {"word": "kill", "start": 11.9, "end": 12.3}
                ]
            }
        ]
    }
    return mock_model


@pytest.fixture
def sample_job_data():
    """Sample job data for testing."""
    return {
        "job_id": "test-job-123",
        "filename": "test_video.mp4",
        "content_type": "video/mp4",
        "file_size": 1024000,
        "object_key": "uploads/test-job-123/test_video.mp4",
        "status": "pending",
        "progress": 0,
        "message": "Job created",
        "created_at": "2023-12-01T00:00:00Z",
        "updated_at": "2023-12-01T00:00:00Z"
    }


@pytest.fixture
def sample_transcript():
    """Sample Whisper transcript for testing."""
    return {
        "text": "Player got a double kill and then a triple kill",
        "segments": [
            {
                "start": 0.0,
                "end": 10.0,
                "text": "Player got a double kill and then a triple kill",
                "words": [
                    {"word": "Player", "start": 0.0, "end": 0.5},
                    {"word": "got", "start": 0.5, "end": 0.8},
                    {"word": "a", "start": 0.8, "end": 0.9},
                    {"word": "double", "start": 1.0, "end": 1.5},
                    {"word": "kill", "start": 1.5, "end": 2.0},
                    {"word": "and", "start": 2.0, "end": 2.2},
                    {"word": "then", "start": 2.2, "end": 2.5},
                    {"word": "a", "start": 2.5, "end": 2.6},
                    {"word": "triple", "start": 5.0, "end": 5.5},
                    {"word": "kill", "start": 5.5, "end": 6.0}
                ]
            }
        ]
    }


@pytest.fixture
def sample_video_file(temp_dir):
    """Create a sample video file for testing."""
    video_path = os.path.join(temp_dir, "test_video.mp4")
    
    # Create a dummy file (in real tests, you might use a real small video file)
    with open(video_path, "wb") as f:
        f.write(b"fake video content for testing")
    
    return video_path


@pytest.fixture
def mock_ffmpeg():
    """Mock FFmpeg operations."""
    with patch('ffmpeg.input') as mock_input, \
         patch('ffmpeg.output') as mock_output, \
         patch('ffmpeg.run') as mock_run, \
         patch('ffmpeg.probe') as mock_probe:
        
        # Mock FFmpeg probe response
        mock_probe.return_value = {
            "format": {
                "duration": "30.0",
                "size": "1024000"
            },
            "streams": [
                {
                    "codec_type": "video",
                    "codec_name": "h264",
                    "width": 1920,
                    "height": 1080,
                    "r_frame_rate": "30/1"
                }
            ]
        }
        
        # Mock FFmpeg processing
        mock_run.return_value = None
        
        yield {
            "input": mock_input,
            "output": mock_output,
            "run": mock_run,
            "probe": mock_probe
        }
