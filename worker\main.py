"""
Video processing worker main entry point.
Consumes jobs from Redis queue and processes videos for highlight extraction.
"""

import asyncio
import logging
import signal
import sys
from typing import Optional

from core.config import get_settings
from core.logging import setup_logging
from core.worker import VideoProcessingWorker

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Global worker instance for graceful shutdown
worker_instance: Optional[VideoProcessingWorker] = None


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    if worker_instance:
        worker_instance.stop()
    sys.exit(0)


async def main():
    """Main worker function."""
    global worker_instance
    
    settings = get_settings()
    logger.info("Starting Video Processing Worker")
    logger.info(f"Environment: {settings.ENVIRONMENT}")
    logger.info(f"Redis URL: {settings.REDIS_URL}")
    logger.info(f"Whisper Model: {settings.WHISPER_MODEL}")
    logger.info(f"Worker Concurrency: {settings.WORKER_CONCURRENCY}")
    
    # Setup signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Create and start worker
        worker_instance = VideoProcessingWorker()
        await worker_instance.start()
        
        # Keep the worker running
        while worker_instance.is_running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Worker failed with error: {e}", exc_info=True)
        raise
    finally:
        if worker_instance:
            await worker_instance.stop()
        logger.info("Video Processing Worker stopped")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Worker interrupted by user")
    except Exception as e:
        logger.error(f"Worker failed to start: {e}", exc_info=True)
        sys.exit(1)
