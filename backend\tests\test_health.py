"""
Tests for health check endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient


def test_health_check(client: TestClient):
    """Test basic health check endpoint."""
    response = client.get("/health")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert data["service"] == "video-highlight-extractor-api"


@pytest.mark.asyncio
async def test_detailed_health_check_healthy(async_client, mock_redis_client, mock_s3_client):
    """Test detailed health check when all services are healthy."""
    # Mock successful service checks
    mock_redis_client.ping.return_value = True
    mock_s3_client.list_buckets.return_value = {"Buckets": []}
    
    response = await async_client.get("/health/detailed")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "healthy"
    assert "checks" in data
    assert data["checks"]["redis"]["status"] == "healthy"
    assert data["checks"]["s3"]["status"] == "healthy"
    assert data["checks"]["job_queue"]["status"] == "healthy"


@pytest.mark.asyncio
async def test_detailed_health_check_unhealthy_redis(async_client, mock_redis_client, mock_s3_client):
    """Test detailed health check when Redis is unhealthy."""
    # Mock Redis failure
    mock_redis_client.ping.side_effect = Exception("Redis connection failed")
    mock_s3_client.list_buckets.return_value = {"Buckets": []}
    
    response = await async_client.get("/health/detailed")
    
    assert response.status_code == 503
    data = response.json()
    
    assert data["detail"]["status"] == "unhealthy"
    assert data["detail"]["checks"]["redis"]["status"] == "unhealthy"
    assert "Redis connection failed" in data["detail"]["checks"]["redis"]["message"]


@pytest.mark.asyncio
async def test_detailed_health_check_unhealthy_s3(async_client, mock_redis_client, mock_s3_client):
    """Test detailed health check when S3 is unhealthy."""
    # Mock S3 failure
    mock_redis_client.ping.return_value = True
    mock_s3_client.list_buckets.side_effect = Exception("S3 connection failed")
    
    response = await async_client.get("/health/detailed")
    
    assert response.status_code == 503
    data = response.json()
    
    assert data["detail"]["status"] == "unhealthy"
    assert data["detail"]["checks"]["s3"]["status"] == "unhealthy"
    assert "S3 connection failed" in data["detail"]["checks"]["s3"]["message"]


def test_get_system_stats(client: TestClient):
    """Test system statistics endpoint."""
    response = client.get("/stats")
    
    assert response.status_code == 200
    data = response.json()
    
    assert "timestamp" in data
    assert "job_stats" in data
    assert "system_info" in data
    assert data["system_info"]["service"] == "video-highlight-extractor-api"


def test_root_endpoint(client: TestClient):
    """Test root endpoint."""
    response = client.get("/")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["message"] == "Video Highlight Extractor API"
    assert data["version"] == "1.0.0"
    assert data["docs"] == "/docs"
    assert data["health"] == "/health"


def test_info_endpoint(client: TestClient):
    """Test info endpoint."""
    response = client.get("/info")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["app_name"] == "Video Highlight Extractor"
    assert data["version"] == "1.0.0"
    assert "supported_formats" in data
    assert "highlight_phrases" in data
    assert "mp4" in data["supported_formats"]
    assert "double kill" in data["highlight_phrases"]
