#!/usr/bin/env python3
"""
Process the full sample video with EXACT phrase matching and save clips locally
"""

import os
import sys
import asyncio
import shutil
from datetime import datetime
from dotenv import load_dotenv

# Load environment
load_dotenv('.env')

# Import our exact processor
from exact_video_processor import ExactVideoProcessor

def create_output_directory():
    """Create output directory for clips"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"extracted_clips_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

async def process_full_video_exact():
    """Process the full video with exact phrase matching"""
    print("🎮 Processing Full Video with EXACT Phrase Matching")
    print("=" * 70)
    
    # Video file path
    video_path = "backend/samples/24 Kills + MANIAC!! Alucard Best Build Exp Lane!! - Build Top 1 Global Alucard ~ MLBB.mp4"
    
    if not os.path.exists(video_path):
        print("❌ Sample video not found!")
        return False
    
    # Get video info
    file_size = os.path.getsize(video_path)
    print(f"📹 Video: {os.path.basename(video_path)}")
    print(f"📊 Size: {file_size / (1024*1024):.1f} MB")
    
    # Create output directory
    output_dir = create_output_directory()
    print(f"📁 Output directory: {output_dir}")
    
    try:
        # Initialize exact processor
        processor = ExactVideoProcessor()
        print("✅ Exact video processor initialized")
        
        # Show exact phrases we're looking for
        print("\n🎯 Exact Phrases to Match:")
        for event, phrases in processor.exact_phrases.items():
            print(f"   {event}: {phrases}")
        
        print(f"\n🔊 Step 1: Loading Whisper model and extracting transcript...")
        
        # Initialize Whisper
        await processor.initialize()
        
        # Extract full transcript
        transcript = await processor.extract_transcript(video_path)
        segments = transcript.get('segments', [])
        print(f"✅ Full transcript extracted: {len(segments)} segments")
        
        # Show transcript summary
        total_duration = transcript.get('duration', 0)
        print(f"📊 Video duration: {total_duration:.1f} seconds ({total_duration/60:.1f} minutes)")
        
        # Show some transcript content
        print(f"\n📝 Transcript Preview (first 10 segments):")
        for i, segment in enumerate(segments[:10]):
            text = segment.get('text', '').strip()
            start = segment.get('start', 0)
            end = segment.get('end', 0)
            print(f"   [{start:6.1f}s-{end:6.1f}s]: {text}")
        
        if len(segments) > 10:
            print(f"   ... and {len(segments) - 10} more segments")
        
        print(f"\n🎯 Step 2: Detecting EXACT gaming event matches...")
        
        # Detect exact highlights
        highlight_ranges = await processor.detect_highlights_exact(transcript)
        
        if highlight_ranges:
            print(f"✅ Found {len(highlight_ranges)} EXACT matches!")
            
            print(f"\n🎬 Exact Gaming Events Detected:")
            total_highlight_duration = 0
            for i, (start, end, event) in enumerate(highlight_ranges):
                duration = end - start
                total_highlight_duration += duration
                print(f"   {i+1:2d}. {event:20s}: {start:6.1f}s - {end:6.1f}s ({duration:5.1f}s)")
            
            print(f"\n📊 Summary:")
            print(f"   • Total clips: {len(highlight_ranges)}")
            print(f"   • Total duration: {total_highlight_duration:.1f} seconds")
            print(f"   • Average clip: {total_highlight_duration/len(highlight_ranges):.1f} seconds")
            
            # Categorize events
            events_found = {}
            for _, _, event in highlight_ranges:
                base_event = event.split('_')[0]  # Remove _sequence suffix
                events_found[base_event] = events_found.get(base_event, 0) + 1
            
            print(f"\n🏆 Events Breakdown:")
            for event, count in sorted(events_found.items()):
                print(f"   • {event}: {count} clips")
            
            print(f"\n🎞️  Step 3: Creating video clips...")
            
            # Create clips
            clip_paths = await processor.create_clips_with_names(
                video_path, highlight_ranges, output_dir
            )
            
            print(f"✅ Created {len(clip_paths)} clips successfully!")
            
            # Show created files
            print(f"\n📁 Created Files:")
            total_size = 0
            for clip_path in clip_paths:
                if os.path.exists(clip_path):
                    size = os.path.getsize(clip_path)
                    total_size += size
                    print(f"   • {os.path.basename(clip_path)} ({size/(1024*1024):.1f} MB)")
                else:
                    print(f"   ❌ {os.path.basename(clip_path)} (failed)")
            
            print(f"\n📊 Final Results:")
            print(f"   • Input video: {file_size/(1024*1024):.1f} MB")
            print(f"   • Output clips: {total_size/(1024*1024):.1f} MB")
            print(f"   • Compression ratio: {(total_size/file_size)*100:.1f}%")
            print(f"   • Clips directory: {output_dir}")
            
            # Create summary file
            summary_path = os.path.join(output_dir, "highlights_summary.txt")
            with open(summary_path, 'w') as f:
                f.write("Gaming Highlights Extraction Summary\n")
                f.write("=" * 40 + "\n\n")
                f.write(f"Source Video: {os.path.basename(video_path)}\n")
                f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total Clips: {len(highlight_ranges)}\n")
                f.write(f"Total Duration: {total_highlight_duration:.1f} seconds\n\n")
                
                f.write("Detected Events:\n")
                for i, (start, end, event) in enumerate(highlight_ranges):
                    duration = end - start
                    f.write(f"{i+1:2d}. {event:20s}: {start:6.1f}s - {end:6.1f}s ({duration:5.1f}s)\n")
                
                f.write(f"\nEvents Breakdown:\n")
                for event, count in sorted(events_found.items()):
                    f.write(f"• {event}: {count} clips\n")
            
            print(f"✅ Summary saved to: {summary_path}")
            
            return {
                'success': True,
                'clips_created': len(clip_paths),
                'total_duration': total_highlight_duration,
                'events_found': events_found,
                'output_dir': output_dir
            }
        
        else:
            print("⚠️  No EXACT gaming event matches found!")
            print("\n💡 This could mean:")
            print("   • Gaming events use different phrasing")
            print("   • Audio quality affects recognition")
            print("   • Events are in different language")
            print("   • No gaming announcements in this video")
            
            # Show what was actually transcribed
            print(f"\n📝 Full Transcript for Analysis:")
            for i, segment in enumerate(segments):
                text = segment.get('text', '').strip()
                start = segment.get('start', 0)
                end = segment.get('end', 0)
                print(f"   [{start:6.1f}s-{end:6.1f}s]: {text}")
            
            return {
                'success': True,
                'clips_created': 0,
                'total_duration': 0,
                'events_found': {},
                'output_dir': output_dir
            }
    
    except Exception as e:
        print(f"❌ Error processing video: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False}

def main():
    """Run the full video processing"""
    print("🚀 Video Highlight Extractor - Full Video Processing")
    print("EXACT phrase matching for gaming events")
    print("Only creates clips for EXACT matches!")
    
    result = asyncio.run(process_full_video_exact())
    
    if result['success']:
        print("\n" + "=" * 70)
        print("🎉 FULL VIDEO PROCESSING COMPLETE!")
        
        if result['clips_created'] > 0:
            print(f"\n🏆 SUCCESS METRICS:")
            print(f"   • Clips created: {result['clips_created']}")
            print(f"   • Total duration: {result['total_duration']:.1f} seconds")
            print(f"   • Events found: {len(result['events_found'])}")
            print(f"   • Output folder: {result['output_dir']}")
            
            print(f"\n🎮 Gaming Events Detected:")
            for event, count in result['events_found'].items():
                print(f"   • {event}: {count} clips")
            
            print(f"\n📁 Your clips are ready in: {result['output_dir']}")
            print(f"   Each clip is named with the exact event detected!")
        else:
            print(f"\n⚠️  No exact matches found")
            print(f"   Check the transcript output above")
            print(f"   Gaming events might use different phrasing")
    else:
        print("\n❌ Processing failed")
    
    return result['success']

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
