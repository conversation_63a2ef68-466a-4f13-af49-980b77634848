#!/bin/bash

# Development setup script

set -e

echo "Setting up Video Highlight Extractor development environment..."

# Check if <PERSON><PERSON> is running
if ! docker info >/dev/null 2>&1; then
    echo "ERROR: Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose >/dev/null 2>&1 && ! docker compose version >/dev/null 2>&1; then
    echo "ERROR: Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example..."
    cp .env.example .env
    echo "✓ Created .env file"
else
    echo "✓ .env file already exists"
fi

# Create necessary directories
echo "Creating necessary directories..."
mkdir -p logs
mkdir -p data/redis
mkdir -p data/localstack
mkdir -p temp/video_processing

# Make scripts executable
echo "Making scripts executable..."
chmod +x scripts/*.sh

# Pull required Docker images
echo "Pulling Docker images..."
docker-compose pull

echo "Development environment setup complete!"
echo ""
echo "To start the application:"
echo "  docker-compose up --build"
echo ""
echo "To start with development profile (includes Red<PERSON> Commander):"
echo "  docker-compose --profile dev up --build"
echo ""
echo "Services will be available at:"
echo "  Frontend:        http://localhost:3000"
echo "  API:             http://localhost:8000"
echo "  API Docs:        http://localhost:8000/docs"
echo "  Redis Commander: http://localhost:8081 (dev profile only)"
