"""
Enhanced video processor with EXACT phrase matching for gaming events
"""

import asyncio
import logging
import os
import re
from typing import List, Dict, Any, Tuple, Optional

import whisper
import ffmpeg
from dotenv import load_dotenv

# Load environment
load_dotenv('.env')

# Add paths
import sys
sys.path.append('worker')
from core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class ExactVideoProcessor:
    """Video processor with exact phrase matching for gaming events."""
    
    def __init__(self):
        self.whisper_model = None
        self.highlight_phrases = settings.HIGHLIGHT_PHRASES
        self.multi_kill_sequences = settings.MULTI_KILL_SEQUENCES
        self.merge_gap_threshold = settings.MERGE_GAP_THRESHOLD
        
        # Define exact phrases to match (case-insensitive)
        self.exact_phrases = {
            "double_kill": ["double kill"],
            "first_blood": ["first blood"],
            "triple_kill": ["triple kill"],
            "mega_kill": ["mega kill"],
            "monster_kill": ["monster kill"],
            "maniac": ["maniac"],
            "savage": ["savage"],
            "godlike": ["godlike"],
            "legendary": ["legendary"],
            "unstoppable": ["unstoppable"],
            "killing_spree": ["killing spree"],
            "you_have_slain_an_enemy": ["you have slain an enemy", "enemy slain", "slain an enemy"]
        }
    
    async def initialize(self):
        """Initialize the video processor (load Whisper model)."""
        logger.info(f"Loading Whisper model: {settings.WHISPER_MODEL}")
        
        # Load Whisper model in a thread to avoid blocking
        loop = asyncio.get_event_loop()
        self.whisper_model = await loop.run_in_executor(
            None,
            whisper.load_model,
            "tiny"  # Use tiny model (already downloaded)
        )
        
        logger.info("Whisper model loaded successfully")
    
    async def extract_transcript(self, video_path: str) -> Dict[str, Any]:
        """Extract transcript from video using Whisper with word-level timestamps."""
        logger.info(f"Extracting transcript from {video_path}")
        
        if not self.whisper_model:
            raise RuntimeError("Whisper model not initialized")
        
        # Run Whisper transcription in a thread
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            lambda: self.whisper_model.transcribe(
                video_path,
                word_timestamps=True,
                verbose=True,
                language="en"  # Force English for gaming content
            )
        )
        
        logger.info(f"Transcript extracted: {len(result.get('segments', []))} segments")
        return result
    
    async def detect_highlights_exact(self, transcript: Dict[str, Any]) -> List[Tuple[float, float, str]]:
        """
        Detect highlight moments with EXACT phrase matching.
        
        Returns:
            List of (start_time, end_time, event_name) tuples for exact matches only
        """
        logger.info("Detecting highlights with EXACT phrase matching")
        
        segments = transcript.get("segments", [])
        if not segments:
            logger.warning("No segments found in transcript")
            return []
        
        # Extract all text with timestamps
        all_text_with_times = []
        for segment in segments:
            words = segment.get("words", [])
            for word_info in words:
                word_text = word_info.get("word", "").strip()
                start_time = word_info.get("start", 0)
                end_time = word_info.get("end", 0)
                all_text_with_times.append({
                    "text": word_text,
                    "start": start_time,
                    "end": end_time
                })
        
        # Find exact phrase matches
        exact_matches = []
        
        for event_name, phrase_variations in self.exact_phrases.items():
            for phrase in phrase_variations:
                matches = self._find_exact_phrase_matches(all_text_with_times, phrase)
                for match_time in matches:
                    exact_matches.append({
                        'event': event_name,
                        'phrase': phrase,
                        'time': match_time,
                        'config': self.highlight_phrases[event_name]
                    })
                    logger.info(f"EXACT MATCH: '{phrase}' at {match_time:.2f}s")
        
        # Sort matches by time
        exact_matches.sort(key=lambda x: x['time'])
        
        # Create highlight ranges with exact event names
        highlight_ranges = []
        processed_matches = set()
        
        for i, match in enumerate(exact_matches):
            if i in processed_matches:
                continue
            
            event_name = match['event']
            match_time = match['time']
            config = match['config']
            start_offset, end_offset, is_sequence = config
            
            # Check if this is a sequence event
            if is_sequence and event_name in self.multi_kill_sequences:
                sequence_range = self._process_exact_sequence(
                    event_name, match, exact_matches, i, processed_matches
                )
                if sequence_range:
                    highlight_ranges.append(sequence_range)
            else:
                # Single event highlight
                clip_start = max(0, match_time + start_offset)
                clip_end = match_time + end_offset
                highlight_ranges.append((clip_start, clip_end, event_name))
                processed_matches.add(i)
                logger.info(f"Created clip for '{event_name}': {clip_start:.2f}s-{clip_end:.2f}s")
        
        # Merge overlapping ranges (but keep event names)
        merged_ranges = self._merge_overlapping_ranges_with_events(highlight_ranges)
        
        logger.info(f"Detected {len(merged_ranges)} exact highlight clips")
        return merged_ranges
    
    def _find_exact_phrase_matches(self, words_with_times: List[Dict], target_phrase: str) -> List[float]:
        """Find exact phrase matches in the word list."""
        matches = []
        target_words = target_phrase.lower().split()
        
        for i in range(len(words_with_times) - len(target_words) + 1):
            # Extract sequence of words
            word_sequence = []
            for j in range(len(target_words)):
                if i + j < len(words_with_times):
                    word_text = words_with_times[i + j]["text"].lower().strip()
                    # Remove punctuation
                    word_text = re.sub(r'[^\w\s]', '', word_text)
                    word_sequence.append(word_text)
            
            # Check for exact match
            if len(word_sequence) == len(target_words):
                # Join and compare
                sequence_text = " ".join(word_sequence)
                target_text = " ".join(target_words)
                
                if sequence_text == target_text:
                    # Found exact match, return timestamp of first word
                    match_time = words_with_times[i]["start"]
                    matches.append(match_time)
                    logger.info(f"EXACT PHRASE MATCH: '{target_phrase}' at {match_time:.2f}s")
        
        return matches
    
    def _process_exact_sequence(
        self, 
        target_event: str, 
        target_match: Dict, 
        all_matches: List[Dict], 
        target_index: int,
        processed_matches: set
    ) -> Tuple[float, float, str]:
        """Process multi-kill sequences with exact matching."""
        sequence_definition = self.multi_kill_sequences.get(target_event, [])
        if not sequence_definition:
            return None
        
        target_time = target_match['time']
        sequence_window = 30.0  # Look within 30 seconds
        
        # Handle double_kill special case with multiple possible sequences
        if target_event == "double_kill" and isinstance(sequence_definition[0], list):
            best_sequence = None
            best_matches = []
            
            for possible_sequence in sequence_definition:
                sequence_matches = self._find_exact_sequence_matches(
                    possible_sequence, target_match, all_matches, target_index, sequence_window
                )
                
                if len(sequence_matches) > len(best_matches):
                    best_sequence = possible_sequence
                    best_matches = sequence_matches
            
            if best_matches:
                return self._create_exact_sequence_clip(best_matches, target_event, processed_matches)
        else:
            # Standard sequence processing
            sequence_matches = self._find_exact_sequence_matches(
                sequence_definition, target_match, all_matches, target_index, sequence_window
            )
            
            if sequence_matches:
                return self._create_exact_sequence_clip(sequence_matches, target_event, processed_matches)
        
        # Fallback to single event
        config = target_match['config']
        start_offset, end_offset, _ = config
        clip_start = max(0, target_time + start_offset)
        clip_end = target_time + end_offset
        processed_matches.add(target_index)
        return (clip_start, clip_end, target_event)
    
    def _find_exact_sequence_matches(
        self, 
        sequence_events: List[str], 
        target_match: Dict, 
        all_matches: List[Dict], 
        target_index: int, 
        sequence_window: float
    ) -> List[Tuple[int, Dict]]:
        """Find exact matches for a sequence of events."""
        target_time = target_match['time']
        sequence_matches = []
        
        # Look backwards for sequence events
        for i in range(target_index - 1, -1, -1):
            match = all_matches[i]
            time_diff = target_time - match['time']
            
            if time_diff > sequence_window:
                break
            
            if match['event'] in sequence_events:
                sequence_matches.append((i, match))
        
        # Add target match
        sequence_matches.append((target_index, target_match))
        
        # Sort by time
        sequence_matches.sort(key=lambda x: x[1]['time'])
        
        return sequence_matches
    
    def _create_exact_sequence_clip(
        self, 
        sequence_matches: List[Tuple[int, Dict]], 
        target_event: str, 
        processed_matches: set
    ) -> Tuple[float, float, str]:
        """Create a clip from exact sequence matches."""
        if len(sequence_matches) < 2:
            match_index, match_data = sequence_matches[0]
            config = match_data['config']
            start_offset, end_offset, _ = config
            clip_start = max(0, match_data['time'] + start_offset)
            clip_end = match_data['time'] + end_offset
            processed_matches.add(match_index)
            return (clip_start, clip_end, target_event)
        
        # Calculate sequence timing
        first_match = sequence_matches[0][1]
        last_match = sequence_matches[-1][1]
        
        first_config = first_match['config']
        last_config = last_match['config']
        
        clip_start = max(0, first_match['time'] + first_config[0])
        clip_end = last_match['time'] + last_config[1]
        
        # Mark all as processed
        for match_index, _ in sequence_matches:
            processed_matches.add(match_index)
        
        sequence_events = [match[1]['event'] for match in sequence_matches]
        logger.info(f"EXACT SEQUENCE: {' -> '.join(sequence_events)} ({clip_start:.2f}s-{clip_end:.2f}s)")
        
        return (clip_start, clip_end, f"{target_event}_sequence")
    
    def _merge_overlapping_ranges_with_events(self, ranges: List[Tuple[float, float, str]]) -> List[Tuple[float, float, str]]:
        """Merge overlapping ranges while preserving event names."""
        if not ranges:
            return []
        
        # Sort by start time
        sorted_ranges = sorted(ranges, key=lambda x: x[0])
        merged = [sorted_ranges[0]]
        
        for current_start, current_end, current_event in sorted_ranges[1:]:
            last_start, last_end, last_event = merged[-1]
            
            # Check if ranges overlap or are close
            if current_start <= last_end + self.merge_gap_threshold:
                # Merge ranges, combine event names
                new_end = max(last_end, current_end)
                combined_event = f"{last_event}+{current_event}" if last_event != current_event else last_event
                merged[-1] = (last_start, new_end, combined_event)
            else:
                merged.append((current_start, current_end, current_event))
        
        return merged
    
    async def create_clips_with_names(
        self, 
        input_video_path: str, 
        highlight_ranges: List[Tuple[float, float, str]], 
        output_dir: str
    ) -> List[str]:
        """Create highlight clips with event-specific names."""
        logger.info(f"Creating {len(highlight_ranges)} clips with exact event names")
        
        clip_paths = []
        
        for i, (start_time, end_time, event_name) in enumerate(highlight_ranges):
            # Create filename with event name and timestamp
            clip_filename = f"{event_name}_{start_time:.1f}s-{end_time:.1f}s.mp4"
            clip_path = os.path.join(output_dir, clip_filename)
            
            duration = end_time - start_time
            
            try:
                await self._extract_clip_ffmpeg(input_video_path, clip_path, start_time, duration)
                clip_paths.append(clip_path)
                logger.info(f"Created: {clip_filename}")
                
            except Exception as e:
                logger.error(f"Failed to create {clip_filename}: {e}")
                continue
        
        return clip_paths
    
    async def _extract_clip_ffmpeg(self, input_path: str, output_path: str, start_time: float, duration: float):
        """Extract a video clip using FFmpeg."""
        stream = ffmpeg.input(input_path, ss=start_time, t=duration)
        stream = ffmpeg.output(
            stream,
            output_path,
            vcodec='copy',
            acodec='copy',
            avoid_negative_ts='make_zero'
        )
        
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            None,
            lambda: ffmpeg.run(stream, overwrite_output=True, quiet=True)
        )
