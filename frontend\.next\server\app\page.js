(()=>{var e={};e.id=974;e.ids=[974];e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1873:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,1154,23));Promise.resolve().then(s.t.bind(s,8620,23));Promise.resolve().then(s.t.bind(s,9280,23));Promise.resolve().then(s.t.bind(s,9003,23));Promise.resolve().then(s.t.bind(s,7595,23));Promise.resolve().then(s.t.bind(s,6643,23));Promise.resolve().then(s.t.bind(s,5523,23));Promise.resolve().then(s.t.bind(s,6957,23))},2119:()=>{},2992:(e,r,s)=>{Promise.resolve().then(s.bind(s,3908))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3908:(e,r,s)=>{"use strict";s.r(r);s.d(r,{"default":()=>a});var t=s(1795);var n=s.n(t);const a=(0,t.registerClientReference)(function(){throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\apps\\\\ML2\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\apps\\ML2\\frontend\\src\\app\\page.tsx","default")},5249:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,7988,23));Promise.resolve().then(s.t.bind(s,3762,23));Promise.resolve().then(s.t.bind(s,2898,23));Promise.resolve().then(s.t.bind(s,2781,23));Promise.resolve().then(s.t.bind(s,5129,23));Promise.resolve().then(s.t.bind(s,4113,23));Promise.resolve().then(s.t.bind(s,2777,23));Promise.resolve().then(s.t.bind(s,8851,23))},5513:()=>{},5775:(e,r,s)=>{"use strict";s.r(r);s.d(r,{"default":()=>c,metadata:()=>d});var t=s(2109);var n=s.n(t);var a=s(8153);var i=s.n(a);var o=s(2119);var l=s.n(o);const d={title:"Video Highlight Extractor",description:"AI-powered gaming highlight extraction from uploaded videos",keywords:["video","highlights","gaming","AI","clips"],authors:[{name:"Video Highlight Team"}],viewport:"width=device-width, initial-scale=1"};function c({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsx)("body",{className:i().className,children:(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"\uD83C\uDFAE Video Highlight Extractor"})}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"AI-Powered Gaming Clips"})]})})}),(0,t.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e}),(0,t.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsx)("div",{className:"text-center text-sm text-gray-500",children:(0,t.jsx)("p",{children:"\xa9 2024 Video Highlight Extractor. Built with Next.js, FastAPI, and AI."})})})})]})})})}},5822:(e,r,s)=>{"use strict";s.r(r);s.d(r,{"default":()=>o});var t=s(5351);var n=s.n(t);var a=s(2674);var i=s.n(a);Object(function e(){var e=new Error("Cannot find module '@/components/VideoUploader'");e.code="MODULE_NOT_FOUND";throw e}());Object(function e(){var e=new Error("Cannot find module '@/components/JobStatus'");e.code="MODULE_NOT_FOUND";throw e}());Object(function e(){var e=new Error("Cannot find module '@/components/HighlightsList'");e.code="MODULE_NOT_FOUND";throw e}());Object(function e(){var e=new Error("Cannot find module '__barrel_optimize__?names=Download,Play,Upload,Zap!=!lucide-react'");e.code="MODULE_NOT_FOUND";throw e}());function o(){const[e,r]=(0,a.useState)(null);const[s,n]=(0,a.useState)("idle");const[i,o]=(0,a.useState)([]);const l=()=>{n("uploading");r(null);o([])};const d=e=>{r(e);n("processing")};const c=e=>{n("completed");o(e)};const m=()=>{n("error")};const p=()=>{r(null);n("idle");o([])};return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsxs)("h2",{className:"text-4xl font-bold text-gray-900 sm:text-5xl",children:["Extract Gaming Highlights",(0,t.jsx)("span",{className:"text-primary-600",children:" Automatically"})]}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto text-balance",children:"Upload your gaming videos and let AI find the best moments. Our system detects kills, shutdowns, and epic plays to create highlight clips."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,t.jsxs)("div",{className:"card text-center",children:[(0,t.jsx)(Object(function e(){var e=new Error("Cannot find module '__barrel_optimize__?names=Download,Play,Upload,Zap!=!lucide-react'");e.code="MODULE_NOT_FOUND";throw e}()),{className:"w-8 h-8 text-primary-600 mx-auto mb-3"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Easy Upload"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Drag and drop your video files"})]}),(0,t.jsxs)("div",{className:"card text-center",children:[(0,t.jsx)(Object(function e(){var e=new Error("Cannot find module '__barrel_optimize__?names=Download,Play,Upload,Zap!=!lucide-react'");e.code="MODULE_NOT_FOUND";throw e}()),{className:"w-8 h-8 text-primary-600 mx-auto mb-3"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"AI Processing"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Whisper AI detects highlight moments"})]}),(0,t.jsxs)("div",{className:"card text-center",children:[(0,t.jsx)(Object(function e(){var e=new Error("Cannot find module '__barrel_optimize__?names=Download,Play,Upload,Zap!=!lucide-react'");e.code="MODULE_NOT_FOUND";throw e}()),{className:"w-8 h-8 text-primary-600 mx-auto mb-3"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Download Clips"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Get individual highlight videos"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(Object(function e(){var e=new Error("Cannot find module '@/components/VideoUploader'");e.code="MODULE_NOT_FOUND";throw e}()),{onUploadStart:l,onUploadComplete:d,onError:m,disabled:s==="uploading"||s==="processing"}),s!=="idle"&&(0,t.jsx)("button",{onClick:p,className:"btn-secondary w-full",disabled:s==="uploading"||s==="processing",children:"Upload Another Video"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[e&&(0,t.jsx)(Object(function e(){var e=new Error("Cannot find module '@/components/JobStatus'");e.code="MODULE_NOT_FOUND";throw e}()),{jobId:e,onComplete:c,onError:m}),i.length>0&&(0,t.jsx)(Object(function e(){var e=new Error("Cannot find module '@/components/HighlightsList'");e.code="MODULE_NOT_FOUND";throw e}()),{highlights:i}),s==="error"&&(0,t.jsx)("div",{className:"card border-error-200 bg-error-50",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-error-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-error-600 text-sm",children:"!"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-error-900",children:"Processing Error"}),(0,t.jsx)("p",{className:"text-sm text-error-700",children:"Something went wrong while processing your video. Please try again."})]})]})})]})]}),(0,t.jsx)("div",{className:"card bg-primary-50 border-primary-200",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(Object(function e(){var e=new Error("Cannot find module '__barrel_optimize__?names=Download,Play,Upload,Zap!=!lucide-react'");e.code="MODULE_NOT_FOUND";throw e}()),{className:"w-6 h-6 text-primary-600 mt-1"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-primary-900 mb-2",children:"Supported Highlights"}),(0,t.jsx)("p",{className:"text-sm text-primary-800 mb-3",children:"Our AI detects these gaming moments and creates clips with optimal timing:"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 text-sm",children:[(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded",children:"Double Kill"}),(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded",children:"First Blood"}),(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded",children:"Triple Kill"}),(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded",children:"Mega Kill"}),(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded",children:"Monster Kill"}),(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded",children:"Maniac"}),(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded",children:"Godlike"}),(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded",children:"Legendary"}),(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded",children:"Unstoppable"}),(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded",children:"Killing Spree"}),(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded",children:"Enemy Slain"})]})]})]})})]})}},7840:(e,r,s)=>{Promise.resolve().then(s.bind(s,5822))},8537:()=>{},8682:(e,r,s)=>{"use strict";s.r(r);s.d(r,{GlobalError:()=>o.a,__next_app__:()=>j,pages:()=>v,routeModule:()=>N,tree:()=>g});var t=s(1855);var n=s.n(t);var a=s(1616);var i=s(2898);var o=s.n(i);var l=s(3253);var d=s.n(l);var c={};for(const e in l)if(["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)<0)c[e]=()=>l[e];s.d(r,c);const m=()=>Promise.resolve().then(s.bind(s,5775));const p=()=>Promise.resolve().then(s.t.bind(s,7166,23));const x=()=>Promise.resolve().then(s.t.bind(s,8055,23));const h=()=>Promise.resolve().then(s.t.bind(s,6364,23));const u=()=>Promise.resolve().then(s.bind(s,3908));const g={children:["",{children:["__PAGE__",{},{page:[u,"C:\\Users\\<USER>\\OneDrive\\Desktop\\apps\\ML2\\frontend\\src\\app\\page.tsx"]}]},{"layout":[m,"C:\\Users\\<USER>\\OneDrive\\Desktop\\apps\\ML2\\frontend\\src\\app\\layout.tsx"],"not-found":[p,"next/dist/client/components/not-found-error"],"forbidden":[x,"next/dist/client/components/forbidden-error"],"unauthorized":[h,"next/dist/client/components/unauthorized-error"]}]}.children;const v=["C:\\Users\\<USER>\\OneDrive\\Desktop\\apps\\ML2\\frontend\\src\\app\\page.tsx"];const b=s;const y=()=>Promise.resolve();const j={require:b,loadChunk:y};const N=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:g}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e);var t=r.X(0,[437],()=>s(8682));module.exports=t})();