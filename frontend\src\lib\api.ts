import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    
    if (error.response?.status === 404) {
      throw new Error('Service not found. Please check if the backend is running.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error. Please try again later.');
    } else if (error.code === 'ECONNREFUSED') {
      throw new Error('Cannot connect to server. Please check if the backend is running.');
    }
    
    throw error;
  }
);

/**
 * Upload a video file and get a job ID
 */
export async function uploadVideo(
  file: File,
  onProgress?: (progress: number) => void
): Promise<string> {
  try {
    // Step 1: Get pre-signed upload URL
    const { data: uploadData } = await api.post('/upload/presigned-url', {
      filename: file.name,
      content_type: file.type,
      file_size: file.size,
    });

    const { upload_url, job_id, fields } = uploadData;

    // Step 2: Upload file to S3 using pre-signed URL
    const formData = new FormData();
    
    // Add all the fields from the pre-signed URL
    Object.entries(fields).forEach(([key, value]) => {
      formData.append(key, value as string);
    });
    
    // Add the file last
    formData.append('file', file);

    await axios.post(upload_url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    // Step 3: Notify backend that upload is complete
    await api.post('/upload/callback', {
      job_id,
      object_key: fields.key,
    });

    return job_id;
  } catch (error) {
    console.error('Upload error:', error);
    
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 413) {
        throw new Error('File too large. Maximum size is 500MB.');
      } else if (error.response?.status === 400) {
        throw new Error(error.response.data?.detail || 'Invalid file format.');
      }
    }
    
    throw new Error('Upload failed. Please try again.');
  }
}

/**
 * Get job status and progress
 */
export async function getJobStatus(jobId: string) {
  try {
    const { data } = await api.get(`/status/${jobId}`);
    return data;
  } catch (error) {
    console.error('Status fetch error:', error);
    
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 404) {
        throw new Error('Job not found. It may have expired.');
      }
    }
    
    throw new Error('Failed to fetch job status.');
  }
}

/**
 * Health check endpoint
 */
export async function healthCheck() {
  try {
    const { data } = await api.get('/health');
    return data;
  } catch (error) {
    console.error('Health check error:', error);
    throw new Error('Backend service is not available.');
  }
}

/**
 * Get system statistics (optional)
 */
export async function getSystemStats() {
  try {
    const { data } = await api.get('/stats');
    return data;
  } catch (error) {
    console.error('Stats fetch error:', error);
    return null; // Non-critical endpoint
  }
}

export default api;
