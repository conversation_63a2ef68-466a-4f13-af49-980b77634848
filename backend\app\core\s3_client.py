"""
S3 client configuration and utilities.
"""

import logging
from typing import Dict, Any, Optional
from functools import lru_cache
from datetime import datetime, timedelta

import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from botocore.config import Config

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


@lru_cache()
def get_s3_client():
    """Get S3 client instance."""
    config = Config(
        region_name=settings.AWS_REGION,
        retries={'max_attempts': 3, 'mode': 'adaptive'},
        max_pool_connections=50,
    )
    
    return boto3.client(
        's3',
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        endpoint_url=settings.S3_ENDPOINT_URL if settings.S3_ENDPOINT_URL else None,
        config=config,
    )


class S3Manager:
    """S3 operations manager for video processing."""
    
    def __init__(self, s3_client=None):
        self.s3 = s3_client or get_s3_client()
        self.input_bucket = settings.INPUT_BUCKET
        self.output_bucket = settings.OUTPUT_BUCKET
    
    def ensure_buckets_exist(self) -> None:
        """Ensure required S3 buckets exist."""
        for bucket in [self.input_bucket, self.output_bucket]:
            try:
                self.s3.head_bucket(Bucket=bucket)
                logger.info(f"Bucket {bucket} exists")
            except ClientError as e:
                error_code = e.response['Error']['Code']
                if error_code == '404':
                    try:
                        self.s3.create_bucket(Bucket=bucket)
                        logger.info(f"Created bucket {bucket}")
                    except ClientError as create_error:
                        logger.error(f"Failed to create bucket {bucket}: {create_error}")
                        raise
                else:
                    logger.error(f"Error checking bucket {bucket}: {e}")
                    raise
    
    def generate_presigned_upload_url(
        self, 
        object_key: str, 
        content_type: str = "video/mp4",
        expiration: int = 3600
    ) -> Dict[str, Any]:
        """Generate presigned URL for direct upload to S3."""
        try:
            # Generate presigned POST for multipart upload
            response = self.s3.generate_presigned_post(
                Bucket=self.input_bucket,
                Key=object_key,
                Fields={
                    'Content-Type': content_type,
                },
                Conditions=[
                    {'Content-Type': content_type},
                    ['content-length-range', 1, settings.MAX_FILE_SIZE],
                ],
                ExpiresIn=expiration
            )
            
            logger.info(f"Generated presigned upload URL for {object_key}")
            return response
            
        except ClientError as e:
            logger.error(f"Failed to generate presigned URL: {e}")
            raise
    
    def generate_presigned_download_url(
        self, 
        bucket: str,
        object_key: str, 
        expiration: int = 3600
    ) -> str:
        """Generate presigned URL for downloading from S3."""
        try:
            url = self.s3.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket, 'Key': object_key},
                ExpiresIn=expiration
            )
            
            logger.info(f"Generated presigned download URL for {object_key}")
            return url
            
        except ClientError as e:
            logger.error(f"Failed to generate presigned download URL: {e}")
            raise
    
    def object_exists(self, bucket: str, object_key: str) -> bool:
        """Check if object exists in S3."""
        try:
            self.s3.head_object(Bucket=bucket, Key=object_key)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            raise
    
    def get_object_info(self, bucket: str, object_key: str) -> Optional[Dict[str, Any]]:
        """Get object metadata from S3."""
        try:
            response = self.s3.head_object(Bucket=bucket, Key=object_key)
            return {
                'size': response.get('ContentLength', 0),
                'last_modified': response.get('LastModified'),
                'content_type': response.get('ContentType'),
                'etag': response.get('ETag', '').strip('"'),
            }
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return None
            logger.error(f"Failed to get object info for {object_key}: {e}")
            raise
    
    def list_objects(self, bucket: str, prefix: str = "") -> list:
        """List objects in S3 bucket with given prefix."""
        try:
            response = self.s3.list_objects_v2(
                Bucket=bucket,
                Prefix=prefix
            )
            
            objects = []
            for obj in response.get('Contents', []):
                objects.append({
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'],
                    'etag': obj['ETag'].strip('"'),
                })
            
            return objects
            
        except ClientError as e:
            logger.error(f"Failed to list objects in {bucket}/{prefix}: {e}")
            raise
    
    def delete_object(self, bucket: str, object_key: str) -> bool:
        """Delete object from S3."""
        try:
            self.s3.delete_object(Bucket=bucket, Key=object_key)
            logger.info(f"Deleted object {object_key} from {bucket}")
            return True
        except ClientError as e:
            logger.error(f"Failed to delete object {object_key}: {e}")
            return False
    
    def copy_object(self, source_bucket: str, source_key: str, dest_bucket: str, dest_key: str) -> bool:
        """Copy object between S3 locations."""
        try:
            copy_source = {'Bucket': source_bucket, 'Key': source_key}
            self.s3.copy_object(
                CopySource=copy_source,
                Bucket=dest_bucket,
                Key=dest_key
            )
            logger.info(f"Copied {source_bucket}/{source_key} to {dest_bucket}/{dest_key}")
            return True
        except ClientError as e:
            logger.error(f"Failed to copy object: {e}")
            return False


def get_s3_manager() -> S3Manager:
    """Get S3 manager instance."""
    return S3Manager()
