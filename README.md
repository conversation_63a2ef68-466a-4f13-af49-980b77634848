# Video Highlight Extractor

A production-ready system that automatically extracts gaming highlights from uploaded videos using AI speech recognition and video processing.

## Features

- **Drag & Drop Upload**: Simple web interface for video uploads
- **AI-Powered Detection**: Uses OpenAI Whisper to detect gaming highlights
- **Automatic Clipping**: Extracts highlight clips with configurable timing
- **Real-time Progress**: Live updates during processing
- **Mobile Responsive**: Works seamlessly on all devices
- **Scalable Architecture**: Microservices with Redis queue and S3 storage

## Quick Start

```bash
# Clone and start the entire system
git clone <your-repo>
cd ML2
docker compose up --build
```

Visit http://localhost:3000 to start uploading videos!

## Architecture

- **Frontend**: Next.js 14 with Type<PERSON> and Tailwind CSS
- **Backend**: FastAPI with Redis for job queuing
- **Worker**: Python service with Whisper AI and FFmpeg
- **Storage**: S3-compatible storage (LocalStack for development)
- **Queue**: Redis for job management and real-time updates

## Development Setup

### Prerequisites
- Docker & Docker Compose
- Node.js 18+ (for local frontend development)
- Python 3.11+ (for local backend development)

### Environment Variables
Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
# Edit .env with your settings
```

### Running Services

```bash
# Start all services
docker compose up --build

# Start individual services
docker compose up frontend
docker compose up api
docker compose up worker
```

### Testing

```bash
# Run all tests
docker compose run --rm api pytest
docker compose run --rm worker pytest

# Run with coverage
docker compose run --rm api pytest --cov=app tests/
```

## API Documentation

Once running, visit:
- Frontend: http://localhost:3000
- API Docs: http://localhost:8000/docs
- Redis Commander: http://localhost:8081

## Deployment

See `terraform/` directory for AWS infrastructure setup.

## License

MIT License
