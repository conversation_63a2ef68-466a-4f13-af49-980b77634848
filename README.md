# 🎮 Video Highlight Extractor

A production-ready system that automatically extracts gaming highlights from uploaded videos using AI speech recognition and video processing.

[![CI/CD Pipeline](https://github.com/your-username/ML2/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/your-username/ML2/actions)
[![Security Scanning](https://github.com/your-username/ML2/workflows/Security%20Scanning/badge.svg)](https://github.com/your-username/ML2/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## ✨ Features

- **🎯 Enhanced AI Detection**: Uses OpenAI Whisper to detect 11 gaming events with multi-kill sequence capture
- **📱 Drag & Drop Upload**: Simple, mobile-responsive web interface
- **⚡ Real-time Progress**: Live updates during video processing
- **🎬 Automatic Clipping**: Extracts highlight clips with optimal timing
- **☁️ Backblaze B2 Storage**: Cost-effective S3-compatible cloud storage with pre-signed URLs
- **🔄 Scalable Architecture**: Microservices with Redis queues
- **🐳 Docker Ready**: Complete containerized development environment
- **🧪 Production Ready**: Comprehensive testing, CI/CD, and monitoring

## 🚀 Quick Start

```bash
# Clone and start the entire system
git clone https://github.com/your-username/ML2.git
cd ML2
make setup
make up-dev
```

Visit **http://localhost:3000** to start uploading videos!

## 🏗️ Architecture

```mermaid
graph TB
    A[Frontend - Next.js 14] --> B[API - FastAPI]
    B --> C[Redis Queue]
    C --> D[Worker - Python]
    D --> E[Whisper AI]
    D --> F[FFmpeg]
    B --> G[S3 Storage]
    D --> G
    H[LocalStack] --> G
```

### Components

- **Frontend**: Next.js 14 with App Router, TypeScript, Tailwind CSS
- **Backend API**: FastAPI with async/await, Redis integration
- **Worker Service**: Python with Whisper AI, FFmpeg video processing
- **Storage**: Backblaze B2 S3-compatible storage (LocalStack for development)
- **Queue**: Redis for job management and real-time pub/sub updates
- **Infrastructure**: Docker Compose, GitHub Actions CI/CD

## 📋 Prerequisites

- **Docker & Docker Compose** (required)
- **Node.js 18+** (for local frontend development)
- **Python 3.11+** (for local backend development)
- **Make** (optional, for convenience commands)

## ⚙️ Installation & Setup

### 1. Environment Configuration

```bash
# Copy and configure environment variables
cp .env.example .env
# Edit .env with your settings (defaults work for development)
```

### 2. Development Setup

```bash
# Automated setup (recommended)
make setup
make up-dev

# Manual setup
chmod +x scripts/*.sh
./scripts/setup-dev.sh
docker-compose --profile dev up --build
```

### 3. Verify Installation

```bash
# Check service health
curl http://localhost:8000/health
curl http://localhost:3000

# View logs
make logs
```

## 🎯 Usage

### Web Interface

1. **Upload Video**: Drag and drop or click to select video files (MP4, AVI, MOV, MKV, WebM)
2. **Processing**: Watch real-time progress as AI analyzes your video
3. **Download Highlights**: Get individual clips of detected highlights

### API Usage

```bash
# Generate upload URL
curl -X POST "http://localhost:8000/upload/presigned-url" \
  -H "Content-Type: application/json" \
  -d '{
    "filename": "gameplay.mp4",
    "content_type": "video/mp4",
    "file_size": 50000000
  }'

# Check job status
curl "http://localhost:8000/status/{job_id}"

# Get highlights
curl "http://localhost:8000/status/{job_id}/highlights"
```

## 🧪 Testing

```bash
# Run all tests
make test

# Individual component tests
make test-frontend    # Jest + React Testing Library
make test-backend     # pytest with async support
make test-worker      # pytest with mocking

# Integration tests
make test-integration

# Coverage reports
make test-backend     # Generates coverage/index.html
make test-worker      # Generates htmlcov/index.html
```

## 🔧 Development

### Local Development

```bash
# Start individual services for development
make dev-frontend     # http://localhost:3000
make dev-api         # http://localhost:8000
make dev-worker      # Background processing

# Database operations
make redis-cli       # Access Redis CLI
make redis-flush     # Clear Redis data
make s3-ls          # List S3 buckets (LocalStack)
```

### Code Quality

```bash
# Linting and formatting
make lint           # All components
make format         # Auto-format code
make security       # Security scans

# Individual components
make lint-frontend
make lint-backend
make lint-worker
```

### Debugging

```bash
# View logs
make logs           # All services
make logs-api       # API only
make logs-worker    # Worker only

# Container access
make shell-api      # API container shell
make shell-worker   # Worker container shell

# Resource monitoring
make top           # Container resource usage
make ps            # Container status
```

## 📊 Monitoring & Observability

### Available Endpoints

- **Frontend**: http://localhost:3000
- **API Documentation**: http://localhost:8000/docs
- **API Health**: http://localhost:8000/health
- **Redis Commander**: http://localhost:8081 (dev profile)

### Metrics & Logging

- Structured logging with timestamps
- Health check endpoints
- Job progress tracking
- Error handling and reporting

## 🚀 Deployment

### Docker Images

```bash
# Build production images
make prod-build

# Start production services
make prod-up
```

### Environment Variables

Key production settings:

```bash
# Redis (production)
REDIS_URL=redis://your-redis-cluster:6379/0

# AWS S3 (production)
AWS_ACCESS_KEY_ID=your_actual_key
AWS_SECRET_ACCESS_KEY=your_actual_secret
S3_ENDPOINT_URL=https://s3.amazonaws.com

# API Configuration
DEBUG=false
LOG_LEVEL=INFO
```

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

### Quick Contribution Setup

```bash
# Fork the repo, then:
git clone https://github.com/YOUR_USERNAME/ML2.git
cd ML2
make setup
make up-dev

# Create feature branch
git checkout -b feature/your-feature-name

# Make changes, test, and submit PR
make test
make lint
```

## 📚 Documentation

- **[API Documentation](http://localhost:8000/docs)** - Interactive API docs
- **[Contributing Guide](CONTRIBUTING.md)** - Development guidelines
- **[Architecture Overview](#architecture)** - System design
- **[Environment Variables](.env.example)** - Configuration options

## 🔒 Security

- Automated security scanning with GitHub Actions
- Dependency vulnerability checks
- Container image scanning
- Secrets detection
- Regular security updates

Report security issues to: [<EMAIL>](mailto:<EMAIL>)

## 📈 Performance

- **Concurrent Processing**: Multiple worker instances
- **Async Operations**: Non-blocking I/O throughout
- **Efficient Storage**: S3 pre-signed URLs for direct upload
- **Optimized Video Processing**: Lossless FFmpeg cutting
- **Caching**: Redis for job state and progress

## 🐛 Troubleshooting

### Common Issues

**Services won't start:**
```bash
make down-clean
make up-dev
```

**Upload fails:**
- Check file size (max 500MB)
- Verify supported format (MP4, AVI, MOV, MKV, WebM)
- Check LocalStack S3 is running

**Worker not processing:**
```bash
make logs-worker
make redis-cli
# Check queue: LLEN video_processing
```

**Frontend build errors:**
```bash
make clean-node
cd frontend && npm ci
```

### Getting Help

1. Check [existing issues](https://github.com/your-username/ML2/issues)
2. Review logs: `make logs`
3. Verify environment: `make ps`
4. Create new issue with details

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI Whisper** - Speech recognition AI
- **FFmpeg** - Video processing
- **FastAPI** - Modern Python web framework
- **Next.js** - React framework
- **Redis** - In-memory data structure store

---

**Made with ❤️ for the gaming community**
