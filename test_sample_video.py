#!/usr/bin/env python3
"""
Test the sample gameplay video through the complete processing pipeline
"""

import os
import sys
import asyncio
import tempfile
import shutil
from datetime import datetime
from dotenv import load_dotenv

# Load environment
load_dotenv('.env')

# Add paths
sys.path.append('backend')
sys.path.append('worker')

def test_sample_video():
    """Test the sample MLBB video through our highlight detection system"""
    print("🎮 Testing Sample Gameplay Video")
    print("=" * 60)
    
    # Video file path
    video_path = "backend/samples/24 Kills + MANIAC!! Alucard Best Build Exp Lane!! - Build Top 1 Global Alucard ~ MLBB.mp4"
    
    if not os.path.exists(video_path):
        print("❌ Sample video not found!")
        return False
    
    # Get video info
    file_size = os.path.getsize(video_path)
    print(f"📹 Video: {os.path.basename(video_path)}")
    print(f"📊 Size: {file_size / (1024*1024):.1f} MB")
    
    try:
        # Test video processor initialization
        from processors.video_processor import VideoProcessor
        processor = VideoProcessor()
        print("✅ Video processor initialized")
        
        # Create temporary directory for processing
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📁 Temp directory: {temp_dir}")
            
            # Copy video to temp directory for processing
            temp_video_path = os.path.join(temp_dir, "sample_video.mp4")
            shutil.copy2(video_path, temp_video_path)
            print("✅ Video copied to temp directory")
            
            async def process_video():
                """Process the video asynchronously"""
                print("\n🔊 Step 1: Extracting transcript with Whisper AI...")
                
                # Initialize Whisper model (use tiny for faster testing)
                import whisper
                model = whisper.load_model("tiny")
                processor.whisper_model = model
                print("✅ Whisper model loaded (tiny for testing)")
                
                # Extract transcript
                transcript = await processor.extract_transcript(temp_video_path)
                print(f"✅ Transcript extracted: {len(transcript.get('segments', []))} segments")
                
                # Print some transcript samples
                segments = transcript.get('segments', [])
                if segments:
                    print("\n📝 Sample transcript segments:")
                    for i, segment in enumerate(segments[:3]):  # Show first 3 segments
                        text = segment.get('text', '').strip()
                        start = segment.get('start', 0)
                        end = segment.get('end', 0)
                        print(f"   {i+1}. [{start:.1f}s-{end:.1f}s]: {text}")
                    
                    if len(segments) > 3:
                        print(f"   ... and {len(segments) - 3} more segments")
                
                print("\n🎯 Step 2: Detecting gaming highlights...")
                
                # Detect highlights
                highlight_ranges = await processor.detect_highlights(transcript)
                print(f"✅ Detected {len(highlight_ranges)} highlight ranges")
                
                if highlight_ranges:
                    print("\n🎬 Detected Highlights:")
                    for i, (start, end) in enumerate(highlight_ranges):
                        duration = end - start
                        print(f"   Clip {i+1}: {start:.1f}s - {end:.1f}s ({duration:.1f}s duration)")
                    
                    print("\n🎞️ Step 3: Creating highlight clips...")
                    
                    # Create clips (limit to first 2 for testing)
                    test_ranges = highlight_ranges[:2] if len(highlight_ranges) > 2 else highlight_ranges
                    clip_paths = await processor.create_clips(temp_video_path, test_ranges, temp_dir)
                    
                    print(f"✅ Created {len(clip_paths)} test clips:")
                    for i, clip_path in enumerate(clip_paths):
                        if os.path.exists(clip_path):
                            clip_size = os.path.getsize(clip_path)
                            print(f"   Clip {i+1}: {os.path.basename(clip_path)} ({clip_size / (1024*1024):.1f} MB)")
                        else:
                            print(f"   Clip {i+1}: ❌ Failed to create")
                    
                    return {
                        'transcript_segments': len(segments),
                        'highlight_ranges': highlight_ranges,
                        'clips_created': len(clip_paths),
                        'success': True
                    }
                else:
                    print("⚠️  No highlights detected in this video")
                    return {
                        'transcript_segments': len(segments),
                        'highlight_ranges': [],
                        'clips_created': 0,
                        'success': True
                    }
            
            # Run the async processing
            result = asyncio.run(process_video())
            
            if result['success']:
                print("\n" + "=" * 60)
                print("🎉 SAMPLE VIDEO PROCESSING COMPLETE!")
                print(f"📊 Results Summary:")
                print(f"   • Transcript segments: {result['transcript_segments']}")
                print(f"   • Highlight ranges: {len(result['highlight_ranges'])}")
                print(f"   • Test clips created: {result['clips_created']}")
                
                if result['highlight_ranges']:
                    print(f"\n🎯 Gaming Events Detected:")
                    # Analyze what types of events were likely detected
                    total_duration = sum(end - start for start, end in result['highlight_ranges'])
                    print(f"   • Total highlight duration: {total_duration:.1f} seconds")
                    print(f"   • Average clip length: {total_duration / len(result['highlight_ranges']):.1f} seconds")
                    
                    # Check for potential multi-kill sequences
                    long_clips = [r for r in result['highlight_ranges'] if (r[1] - r[0]) > 10]
                    if long_clips:
                        print(f"   • Potential multi-kill sequences: {len(long_clips)}")
                        print(f"     (clips longer than 10 seconds)")
                
                return True
            else:
                print("❌ Video processing failed")
                return False
                
    except Exception as e:
        print(f"❌ Error processing sample video: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_detection_capabilities():
    """Show what gaming events our system can detect"""
    print("\n🎮 Gaming Events Detection Capabilities:")

    from processors.video_processor import VideoProcessor
    processor = VideoProcessor()

    print("📋 Configured Gaming Events (12 total):")
    for event, config in processor.highlight_phrases.items():
        start_offset, end_offset, is_sequence = config
        event_type = "Multi-Kill Sequence" if is_sequence else "Single Event"
        print(f"   • {event}: {start_offset}s to +{end_offset}s ({event_type})")

    print("\n🎯 Multi-Kill Sequences:")
    for sequence, events in processor.multi_kill_sequences.items():
        if isinstance(events[0], list):  # double_kill has multiple options
            print(f"   • {sequence}: Multiple possible sequences")
            for i, option in enumerate(events):
                print(f"     Option {i+1}: {' → '.join(option)}")
        else:
            print(f"   • {sequence}: {' → '.join(events)}")

    print("\n🔊 The system will analyze the actual audio content to detect these events...")
    return True

def main():
    """Run the sample video test"""
    print("🚀 Video Highlight Extractor - Sample Video Test")
    print("Testing with MLBB Alucard gameplay")
    
    # Test expected events
    expected_events = test_specific_events()
    
    # Process the video
    success = test_sample_video()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 SAMPLE VIDEO TEST SUCCESSFUL!")
        print("\n💡 Next Steps:")
        print("   1. Start the full system: make up-prod")
        print("   2. Visit http://localhost:3000")
        print("   3. Upload this video through the web interface")
        print("   4. Watch real-time processing")
        print("   5. Download the generated highlight clips!")
        print("\n🎮 Expected Results:")
        print("   • MANIAC sequence clip (12-18 seconds)")
        print("   • Multiple kill event clips")
        print("   • Epic rampage footage!")
    else:
        print("\n❌ Sample video test failed")
        print("Check the error messages above for troubleshooting")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
